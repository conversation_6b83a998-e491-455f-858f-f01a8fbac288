import logging
import os
from typing import Union

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import pandas as pd
import math # 确保导入 math
import traceback # 用于更详细的错误打印
import time # 用于回调
# 导入独立的模型架构
try:
    from model_architectures.lstm import LSTMModel
    from model_architectures.gru import GRUModel
    from model_architectures.transformer import TransformerModel
    from model_architectures.tcn import TCNModel
    from model_architectures.hybrid_lstm_attention import HybridLSTMAttentionModel
    MODEL_ARCHITECTURES_AVAILABLE = True
    logging.info("成功导入自定义模型架构。")
except ImportError as import_err:
    logging.warning(f"无法导入自定义模型架构: {import_err}。"
                    "仅使用内置nn模块作为LSTM/GRU的后备选项。"
                    "手动实现的Transformer/TCN/Hybrid将不可用。")
    # 如果导入失败，定义虚拟类，以便TimeSeriesModel不会立即崩溃
    class LSTMModel(nn.Module): pass
    class GRUModel(nn.Module): pass
    class TransformerModel(nn.Module): pass
    class TCNModel(nn.Module): pass
    class HybridLSTMAttentionModel(nn.Module): pass
    MODEL_ARCHITECTURES_AVAILABLE = False

# Callback System
class BaseCallback:
    """简单的回调基类"""
    def __init__(self):
        self.model = None # 会在训练开始时设置
        self.params = None # 存储训练参数

    def set_model(self, model):
        self.model = model

    def set_params(self, params):
        self.params = params

    def on_train_begin(self, logs=None):
        pass

    def on_train_end(self, logs=None):
        pass

    def on_epoch_begin(self, epoch, logs=None):
        pass

    def on_epoch_end(self, epoch, logs=None):
        pass

    def on_batch_begin(self, batch, logs=None):
        pass

    def on_batch_end(self, batch, logs=None):
        pass

class LoggingCallback(BaseCallback):
    """记录训练过程信息的回调"""
    def __init__(self, log_interval=10):
        super().__init__()
        self.log_interval = log_interval
        self._epoch_start_time = None

    def on_epoch_begin(self, epoch, logs=None):
        self._epoch_start_time = time.time()
        print(f"\n轮次 {epoch + 1}/{self.params['epochs']} - 开始")

    def on_epoch_end(self, epoch, logs=None):
        logs = logs or {}
        epoch_duration = time.time() - self._epoch_start_time
        lr = self.model.optimizer.param_groups[0]['lr'] # 获取当前学习率
        print(f"轮次 {epoch + 1}/{self.params['epochs']} - 结束 - "
              f"耗时: {epoch_duration:.2f}秒 - "
              f"训练损失: {logs.get('train_loss', 'N/A'):.6f} - "
              f"验证损失: {logs.get('val_loss', 'N/A'):.6f} - "
              f"学习率: {lr:.6f}")

    def on_batch_end(self, batch, logs=None):
        # 记录轮次内的进度
        # if batch % self.log_interval == 0:
        #     logs = logs or {}
        #     print(f"  批次 {batch + 1} - 损失: {logs.get('loss', 'N/A'):.6f}")
        pass # 暂时禁用批次日志记录以减少控制台输出

class SimpleModelCheckpoint(BaseCallback):
    """
    简单的模型检查点回调。
    它会在每个epoch结束时检查一个监控的指标(例如验证损失),
    如果该指标相比之前记录的最佳值有所改善，则保存当前模型的权重。
    这个回调只保存单个“最佳”模型文件，新的更好模型会覆盖旧的。
    """
    def __init__(self, filepath="best_model_temp.pth", monitor='val_loss', mode='min', verbose=1):
        """
        初始化 SimpleModelCheckpoint 回调。

        Args:
            filepath (str, optional): 保存最佳模型的文件路径。
                                      默认为 "best_model_temp.pth"。
            monitor (str, optional): 需要监控的指标名称。这个名称应该与训练循环中
                                     传递给 on_epoch_end 的 logs 字典中的键匹配。
                                     默认为 'val_loss' (验证损失)。
            mode (str, optional): 监控模式，可以是 'min' 或 'max'。
                                  - 'min': 当监控的指标越小越好时使用（例如损失）。
                                  - 'max': 当监控的指标越大越好时使用（例如准确率）。
                                  默认为 'min'。
            verbose (int, optional): 控制打印信息的详细程度。
                                     - 0: 不打印信息。
                                     - 1: 当指标改善并保存模型时打印信息。
                                     默认为 1。
        """
        super().__init__()  # 调用父类 BaseCallback 的构造函数
        self.filepath = filepath  # 保存模型的文件路径
        self.monitor = monitor    # 要监控的指标名称 (例如 'val_loss')
        self.verbose = verbose    # 是否打印保存信息

        # 根据监控模式初始化最佳指标值
        # 如果是 'min' 模式，希望找到最小值，所以 best_value 初始设为正无穷大
        # 如果是 'max' 模式，希望找到最大值，所以 best_value 初始设为负无穷大
        if mode not in ['min', 'max']:
            logging.warning(f"SimpleModelCheckpoint: 无效的 mode '{mode}'。将使用 'min' 模式。")
            self.mode = 'min'
        else:
            self.mode = mode

        if self.mode == 'min':
            self.best_value = float('inf')  # 正无穷大
        else: # self.mode == 'max'
            self.best_value = float('-inf') # 负无穷大

    def on_epoch_end(self, epoch, logs=None):
        """
        在每个epoch结束时调用的方法。

        Args:
            epoch (int): 当前的epoch索引（从0开始）。
            logs (dict, optional): 包含当前epoch训练和验证结果的字典。
                                   期望包含一个键与 self.monitor 匹配的项。
                                   例如: {'train_loss': 0.5, 'val_loss': 0.3}
        """
        logs = logs or {}  # 如果 logs 为 None，则初始化为空字典，避免后续 .get() 出错

        # 从 logs 字典中获取当前监控指标的值
        current_value = logs.get(self.monitor)

        # 检查监控的指标是否存在于 logs 中
        if current_value is None:
            logging.warning(f"模型检查点警告 (轮次 {epoch + 1}): "
                            f"监控的指标 '{self.monitor}' 在日志中不可用。无法检查是否保存模型。")
            return

        save_model = False  # 标志位，指示是否需要保存模型

        if self.mode == 'min':
            # 如果是最小化模式，且当前值小于最佳值，则认为有改善
            if current_value < self.best_value:
                save_model = True
        elif self.mode == 'max':
            # 如果是最大化模式，且当前值大于最佳值，则认为有改善
            if current_value > self.best_value:
                save_model = True

        # 如果指标有改善，则保存模型
        if save_model:
            if self.verbose > 0:
                # 如果 verbose 大于0，则打印保存信息
                print(f"SimpleModelCheckpoint: 轮次 {epoch + 1}: "
                      f"{self.monitor} 从 {self.best_value:.6f} 改善到 {current_value:.6f}。"
                      f"正在保存模型到 {self.filepath}")

            # 更新记录的最佳指标值
            self.best_value = current_value

            # 调用模型自身的保存方法来保存模型的状态
            if self.model is not None and hasattr(self.model, 'save_model'):
                self.model.save_model(self.filepath)
            else:
                logging.error(f"SimpleModelCheckpoint: 无法保存模型。 "
                              f"模型对象未设置 (is None: {self.model is None}) 或缺少 'save_model' 方法。")

def calculate_mae(y_true, y_pred):
    """计算 Mean Absolute Error"""
    y_true, y_pred = np.asarray(y_true), np.asarray(y_pred)
    return np.mean(np.abs(y_true - y_pred))

def calculate_rmse(y_true, y_pred):
    """计算 Root Mean Squared Error"""
    y_true, y_pred = np.asarray(y_true), np.asarray(y_pred)
    return np.sqrt(np.mean((y_true - y_pred)**2))

def calculate_r2(y_true, y_pred):
    """计算 R-squared (决定系数)"""
    y_true, y_pred = np.asarray(y_true), np.asarray(y_pred)
    ss_res = np.sum((y_true - y_pred)**2)
    ss_tot = np.sum((y_true - np.mean(y_true))**2)
    if ss_tot == 0: # 如果y_true是常数，避免除以零
        return 1.0 if ss_res < 1e-9 else 0.0
    return 1 - (ss_res / ss_tot)


class TimeSeriesModel(nn.Module):
    """
    通用时间序列模型封装器。

    支持多种核心架构 (LSTM, GRU, Transformer, TCN)，
    可以通过内置 nn 模块或自定义架构实现。
    """
    SUPPORTED_TYPES = ['LSTM', 'GRU', 'TRANSFORMER', 'TCN', 'HYBRID']

    def __init__(self, model_type, input_dim, output_dim=1, config=None, use_builtin_rnn=False):
        """
        初始化 TimeSeriesModel。

        Args:
            model_type (str): 模型类型 ('LSTM', 'GRU', 'Transformer', 'TCN').
            input_dim (int): 输入特征维度。
            output_dim (int): 输出维度 (通常为 1)。 Defaults to 1.
            config (dict, optional): 包含模型超参数的字典。 Defaults to None.
            use_builtin_rnn (bool): 对于 LSTM/GRU，是否强制使用 torch.nn 内置版本。
                                    如果 False 且自定义架构可用，则使用自定义架构。
                                    Defaults to False.
        """
        super().__init__()
        self.original_model_type = model_type
        self.model_type = model_type.upper()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.use_builtin_rnn = use_builtin_rnn # 存储此选择
        self.optimizer = None # 将在训练设置期间设置
        self.scheduler = None # 将在训练设置期间设置

        if self.model_type not in self.SUPPORTED_TYPES:
            raise ValueError(f"不支持的模型类型: {model_type}. 支持的类型: {self.SUPPORTED_TYPES}")

        logging.info(f"初始化TimeSeriesModel封装器，类型: {self.original_model_type}")
        logging.info(f"输入维度: {input_dim}, 输出维度: {output_dim}")
        logging.info(f"使用内置RNN（如适用）: {use_builtin_rnn}")

        # 默认配置
        default_config = self._get_default_config()
        self.config = default_config.copy()
        if config is not None:
            # Only update with keys present in the provided config
            provided_keys = set(config.keys())
            valid_keys = set(default_config.keys())
            update_dict = {k: config[k] for k in provided_keys if k in valid_keys}
            unknown_keys = provided_keys - valid_keys
            if unknown_keys:
                logging.warning(f"忽略未知的配置键: {unknown_keys}")
            self.config.update(update_dict)
        logging.info(f"有效的模型配置: {self.config}")


        # 验证配置
        try:
            self._validate_config(self.model_type, self.config)
        except ValueError as e:
            logging.error(f"配置验证失败: {e}")
            raise

        # 构建核心模型
        logging.info(f"正在为 {self.model_type} 构建核心模型...")
        self.core_model = self._build_core_model(input_dim)
        if self.core_model is None:
             # 如果自定义架构加载失败，并且对于Transformer/TCN，use_builtin_rnn为False，可能会发生这种情况
             raise RuntimeError(f"无法为类型 {self.model_type} 构建核心模型。"
                                f"检查模型架构是否可用，或者对LSTM/GRU启用use_builtin_rnn。")
        logging.info(f"核心模型已构建: {type(self.core_model).__name__}")


        # 获取核心模型输出特征大小并构建最终FC层
        try:
            core_output_feature_size = self._get_core_output_feature_size()
            logging.info(f"确定核心模型输出特征大小: {core_output_feature_size}")
            self.fc = nn.Linear(core_output_feature_size, output_dim)
            logging.info(f"创建最终FC层: Linear({core_output_feature_size}, {output_dim})")
        except Exception as e:
            logging.error(f"确定核心输出大小或创建FC层时出错: {e}")
            traceback.print_exc()
            raise

        # 模型参数统计
        self.total_params = sum(p.numel() for p in self.parameters())
        self.trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        logging.info(f"模型已初始化。总参数: {self.total_params:,}, 可训练参数: {self.trainable_params:,}")

    def _get_default_config(self):
        """返回所有支持参数的默认配置字典"""
        return {
            # Common
            'dropout': 0.1,
            # RNN (LSTM/GRU)
            'hidden_dim': 64,
            'num_layers': 2,
            # Transformer
            'nhead': 4, # hidden_dim必须能被Transformer的nhead整除
            # TCN
            'tcn_num_channels': [32, 64], # 每层的通道列表
            'tcn_kernel_size': 5,
            # Hybrid LSTM-Attention
            'bidirectional': True,  # 是否使用双向LSTM
            'forecast_steps': 1,    # 多步预测的步数
            'use_residual': True,   # 是否使用残差连接
            'attention_dim': None,  # 注意力层的维度，None表示使用hidden_dim
            # 在此处添加其他潜在的未来配置
            'activation': 'relu', # 潜在未来使用的示例
        }

    def _validate_config(self, model_type_to_check, config_to_check):
        """验证模型配置的有效性"""
        logging.debug(f"验证模型类型的配置: {model_type_to_check}")
        cfg = config_to_check
        m_type = model_type_to_check # 在__init__中已转为大写

        # 1. 通用参数检查
        if not isinstance(cfg.get('dropout'), (int, float)) or not (0 <= cfg['dropout'] <= 1):
            raise ValueError(f"无效的 'dropout': {cfg.get('dropout')}。必须是0到1之间的浮点数。")
        # 如果使用，添加对其他常见参数（如'activation'）的检查

        # 2. 特定模型参数检查
        required_keys = []
        if m_type in ['LSTM', 'GRU']:
            required_keys = ['hidden_dim', 'num_layers']
            if not isinstance(cfg.get('hidden_dim'), int) or cfg['hidden_dim'] <= 0:
                raise ValueError(f"无效的 'hidden_dim': {cfg.get('hidden_dim')}。必须是正整数。")
            if not isinstance(cfg.get('num_layers'), int) or cfg['num_layers'] <= 0:
                raise ValueError(f"无效的 'num_layers': {cfg.get('num_layers')}。必须是正整数。")

        elif m_type == 'TRANSFORMER':
            required_keys = ['hidden_dim', 'num_layers', 'nhead']
            if not isinstance(cfg.get('hidden_dim'), int) or cfg['hidden_dim'] <= 0:
                raise ValueError(f"无效的 'hidden_dim': {cfg.get('hidden_dim')}。必须是正整数。")
            if not isinstance(cfg.get('num_layers'), int) or cfg['num_layers'] <= 0:
                raise ValueError(f"无效的 'num_layers': {cfg.get('num_layers')}。必须是正整数。")
            if not isinstance(cfg.get('nhead'), int) or cfg['nhead'] <= 0:
                raise ValueError(f"无效的 'nhead': {cfg.get('nhead')}。必须是正整数。")
            if cfg['hidden_dim'] % cfg['nhead'] != 0:
                raise ValueError(f"'hidden_dim' ({cfg['hidden_dim']}) 必须能被 'nhead' ({cfg['nhead']}) 整除。")

        elif m_type == 'TCN':
            required_keys = ['tcn_num_channels', 'tcn_kernel_size']
            channels = cfg.get('tcn_num_channels')
            if not isinstance(channels, list) or not channels or not all(isinstance(c, int) and c > 0 for c in channels):
                raise ValueError(f"无效的 'tcn_num_channels': {channels}。必须是非空的正整数列表。")
            kernel_size = cfg.get('tcn_kernel_size')
            if not isinstance(kernel_size, int) or kernel_size <= 1: # 核大小必须 > 1
                raise ValueError(f"无效的 'tcn_kernel_size': {kernel_size}。必须是大于1的整数。")

        elif m_type == 'HYBRID':
            required_keys = ['hidden_dim', 'num_layers', 'bidirectional', 'forecast_steps', 'use_residual']
            if not isinstance(cfg.get('hidden_dim'), int) or cfg['hidden_dim'] <= 0:
                raise ValueError(f"无效的 'hidden_dim': {cfg.get('hidden_dim')}。必须是正整数。")
            if not isinstance(cfg.get('num_layers'), int) or cfg['num_layers'] <= 0:
                raise ValueError(f"无效的 'num_layers': {cfg.get('num_layers')}。必须是正整数。")
            if not isinstance(cfg.get('bidirectional'), bool):
                raise ValueError(f"无效的 'bidirectional': {cfg.get('bidirectional')}。必须是布尔值。")
            if not isinstance(cfg.get('forecast_steps'), int) or cfg['forecast_steps'] <= 0:
                raise ValueError(f"无效的 'forecast_steps': {cfg.get('forecast_steps')}。必须是正整数。")
            if not isinstance(cfg.get('use_residual'), bool):
                raise ValueError(f"无效的 'use_residual': {cfg.get('use_residual')}。必须是布尔值。")
            # 检查attention_dim（如果提供）
            if cfg.get('attention_dim') is not None and (not isinstance(cfg['attention_dim'], int) or cfg['attention_dim'] <= 0):
                raise ValueError(f"无效的 'attention_dim': {cfg.get('attention_dim')}。必须是正整数或None。")

        # 检查是否所有必需的键都存在
        missing_keys = [key for key in required_keys if key not in cfg]
        if missing_keys:
            # 如果默认配置处理正确，这种情况不应该发生，但作为安全保障是好的
            raise ValueError(f"模型类型 '{m_type}' 缺少必需的配置键: {missing_keys}")

        logging.debug(f"{m_type} 的配置验证成功。")


    def _build_core_model(self, input_dim: int) -> nn.Module:
        """
        构建核心时序处理模型（不含最终的 fc 层）。
        根据 self.model_type 和 self.use_builtin_rnn 决定使用哪个实现。
        """
        cfg = self.config
        m_type = self.model_type

        if m_type == 'LSTM':
            # 为多层内置LSTM正确确定dropout率
            lstm_dropout = cfg['dropout'] if cfg['num_layers'] > 1 else 0
            if self.use_builtin_rnn or not MODEL_ARCHITECTURES_AVAILABLE:
                logging.info("使用torch.nn.LSTM构建核心模型")
                return nn.LSTM(
                    input_size=input_dim, hidden_size=cfg['hidden_dim'],
                    num_layers=cfg['num_layers'], batch_first=True,
                    dropout=lstm_dropout, bias=True
                )
            else:
                logging.info("使用自定义model_architectures.LSTMModel构建核心模型")
                # 将output_dim作为hidden_dim传递，因为自定义模型包含fc
                # 我们需要在_get_core_output_feature_size中处理这个问题
                try:
                    return LSTMModel(
                        input_dim=input_dim, hidden_dim=cfg['hidden_dim'],
                        output_dim=cfg['hidden_dim'], # 自定义模型的内部fc输出hidden_dim
                        num_layers=cfg['num_layers'], dropout=cfg['dropout']
                    )
                except Exception as e:
                    logging.error(f"实例化自定义LSTMModel失败: {e}。回退到nn.LSTM。")
                    return nn.LSTM(
                        input_size=input_dim, hidden_size=cfg['hidden_dim'],
                        num_layers=cfg['num_layers'], batch_first=True,
                        dropout=lstm_dropout, bias=True
                    )

        elif m_type == 'GRU':
            gru_dropout = cfg['dropout'] if cfg['num_layers'] > 1 else 0
            if self.use_builtin_rnn or not MODEL_ARCHITECTURES_AVAILABLE:
                logging.info("使用torch.nn.GRU构建核心模型")
                return nn.GRU(
                    input_size=input_dim, hidden_size=cfg['hidden_dim'],
                    num_layers=cfg['num_layers'], batch_first=True,
                    dropout=gru_dropout, bias=True
                )
            else:
                logging.info("使用自定义model_architectures.GRUModel构建核心模型")
                try:
                    return GRUModel(
                         input_dim=input_dim, hidden_dim=cfg['hidden_dim'],
                         output_dim=cfg['hidden_dim'], # 自定义模型的内部fc输出hidden_dim
                         num_layers=cfg['num_layers'], dropout=cfg['dropout']
                    )
                except Exception as e:
                    logging.error(f"实例化自定义GRUModel失败: {e}。回退到nn.GRU。")
                    return nn.GRU(
                        input_size=input_dim, hidden_size=cfg['hidden_dim'],
                        num_layers=cfg['num_layers'], batch_first=True,
                        dropout=gru_dropout, bias=True
                    )

        elif m_type == 'TRANSFORMER':
            if not MODEL_ARCHITECTURES_AVAILABLE:
                 logging.error("无法构建Transformer: 自定义模型架构不可用。")
                 return None # 或者抛出错误
            logging.info("使用自定义model_architectures.TransformerModel构建核心模型")
            try:
                 # 我们的占位符TransformerModel的解码器输出`output_dim`，但
                 # 为了在这个封装器中保持一致性，让我们假设*核心*部分在
                 # 最终投影之前结束，输出`hidden_dim`。
                 # 占位符需要调整或者这个封装器需要知道。
                 # 假设占位符TransformerModel在其内部解码器之前输出hidden_dim。
                 # 我们将添加我们自己的最终self.fc层。
                 return TransformerModel(
                    input_dim=input_dim, hidden_dim=cfg['hidden_dim'],
                    output_dim=cfg['hidden_dim'], # 核心输出是hidden_dim
                    nhead=cfg['nhead'], num_layers=cfg['num_layers'],
                    dropout=cfg['dropout']
                )
            except Exception as e:
                 logging.error(f"实例化自定义TransformerModel失败: {e}")
                 traceback.print_exc()
                 return None

        elif m_type == 'TCN':
            if not MODEL_ARCHITECTURES_AVAILABLE:
                 logging.error("无法构建TCN: 自定义模型架构不可用。")
                 return None # 或者抛出错误
            logging.info("使用自定义model_architectures.TCNModel构建核心模型")
            try:
                 # 自定义TCNModel在其内部fc之前输出最后一个通道大小。
                 # 所以，这里的核心输出就是最后一个通道大小。
                 return TCNModel(
                    input_dim=input_dim,
                    # 将最后一个通道大小作为TCNModel的内部output_dim传递
                    output_dim=cfg['tcn_num_channels'][-1],
                    num_channels=cfg['tcn_num_channels'],
                    kernel_size=cfg['tcn_kernel_size'],
                    dropout=cfg['dropout']
                 )
            except Exception as e:
                 logging.error(f"实例化自定义TCNModel失败: {e}")
                 traceback.print_exc()
                 return None

        elif m_type == 'HYBRID':
            if not MODEL_ARCHITECTURES_AVAILABLE:
                 logging.error("无法构建混合模型: 自定义模型架构不可用。")
                 return None
            logging.info("使用自定义model_architectures.HybridLSTMAttentionModel构建核心模型")
            try:
                 # 创建混合LSTM-Attention模型
                 return HybridLSTMAttentionModel(
                    input_dim=input_dim,
                    hidden_dim=cfg['hidden_dim'],
                    output_dim=self.output_dim,  # 使用TimeSeriesModel的output_dim
                    num_layers=cfg['num_layers'],
                    dropout=cfg['dropout'],
                    bidirectional=cfg['bidirectional'],
                    forecast_steps=cfg['forecast_steps'],
                    use_residual=cfg['use_residual']
                 )
            except Exception as e:
                 logging.error(f"实例化自定义HybridLSTMAttentionModel失败: {e}")
                 traceback.print_exc()
                 return None

        else:
            # 由于在__init__中有检查，这种情况不应该发生，但这是防御性编码
            raise ValueError(f"内部错误: _build_core_model遇到不支持的类型 {m_type}")


    def _get_core_output_feature_size(self):
        """确定核心模型（在最终 self.fc 层之前）的输出特征维度"""
        cfg = self.config
        core_model_instance = self.core_model # 实际实例化的核心模型

        if isinstance(core_model_instance, (nn.LSTM, nn.GRU)):
            # 内置RNN从序列输出hidden_dim
            return cfg['hidden_dim']
        elif isinstance(core_model_instance, (LSTMModel, GRUModel)):
            # 自定义RNN占位符也被设计（或假设）在最终封装器fc层之前输出hidden_dim
            return cfg['hidden_dim']
        elif isinstance(core_model_instance, TransformerModel):
            # 假设自定义Transformer占位符在最终封装器fc层之前为最后一个时间步输出hidden_dim特征
            return cfg['hidden_dim']
        elif isinstance(core_model_instance, TCNModel):
            # 自定义TCN占位符输出大小等于最后一个通道计数的特征
            return cfg['tcn_num_channels'][-1]
        elif isinstance(core_model_instance, HybridLSTMAttentionModel):
            # 混合模型的输出特征大小
            # 如果是双向LSTM，输出维度是hidden_dim
            # 如果不是双向LSTM，输出维度是hidden_dim
            return cfg['hidden_dim']
        else:
            raise TypeError(f"无法确定核心模型类型的输出特征大小: {type(core_model_instance)}")


    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        模型的前向传播。

        Args:
            x (torch.Tensor): 输入张量, shape [batch_size, seq_len, input_dim].

        Returns:
            torch.Tensor: 输出张量, shape [batch_size, output_dim].
        """
        # logging.debug(f"Forward pass input shape: {x.shape}") # Can be noisy

        # 1. 通过核心模型
        # 处理可能的形状不匹配或特定模型需求（如有必要）
        # （例如，TCN可能期望[batch, features, seq_len]）
        # TCN占位符现在在内部处理排列。
        try:
            core_output = self.core_model(x)
            # logging.debug(f"核心模型输出类型: {type(core_output)}, 是否为元组: {isinstance(core_output, tuple)}")
            # if isinstance(core_output, tuple):
            #      logging.debug(f"核心模型输出元组形状: {[item.shape if isinstance(item, torch.Tensor) else type(item) for item in core_output]}")
            # elif isinstance(core_output, torch.Tensor):
            #      logging.debug(f"核心模型输出张量形状: {core_output.shape}")

        except Exception as e:
            logging.error(f"核心模型前向传播期间出错: {e}")
            logging.error(f"输入形状为: {x.shape}, 核心模型类型: {type(self.core_model)}")
            traceback.print_exc()
            raise

        # 2. 提取用于最终 FC 层的特征
        features_for_fc = None
        if isinstance(self.core_model, (nn.LSTM, nn.GRU)):
            output_seq, _ = core_output
            features_for_fc = output_seq[:, -1, :]
            # logging.debug(f"从nn.RNN output_seq[:, -1, :]提取的特征，形状: {features_for_fc.shape}")

        # 根据自定义模型实现进行调整
        elif isinstance(self.core_model, (LSTMModel, GRUModel, TransformerModel, TCNModel)):
             # 假设我们的自定义模型（或占位符）被设计为直接返回
             # 用于最终FC层的特征。
             # 占位符LSTM/GRU/Transformer返回[batch, output_dim(==hidden_dim)]
             # TCN返回[batch, output_dim(==last_channel)]
             features_for_fc = core_output
             # logging.debug(f"使用自定义模型{type(self.core_model).__name__}的直接输出作为特征，形状: {features_for_fc.shape}")

        elif isinstance(self.core_model, HybridLSTMAttentionModel):
            # 处理混合模型的输出
            # 如果是多步预测模式，输出形状为 [batch_size, forecast_steps, output_dim]
            # 如果是单步预测模式，输出形状为 [batch_size, output_dim]
            if self.config['forecast_steps'] > 1:
                # 多步预测模式 - 直接返回模型输出，跳过最终的FC层
                # 因为混合模型已经包含了输出层
                return core_output
            else:
                # 单步预测模式 - 使用最终的FC层
                features_for_fc = core_output

        else:
            raise TypeError(f"前向传播中遇到未知的核心模型类型: {type(self.core_model)}")

        if features_for_fc is None:
             raise RuntimeError("无法提取用于FC层的特征。")

        # 3. 通过最终的全连接层
        try:
            final_output = self.fc(features_for_fc)
            # logging.debug(f"FC层后的最终输出形状: {final_output.shape}")
        except Exception as e:
            logging.error(f"最终FC层前向传播期间出错: {e}")
            logging.error(f"输入特征形状为: {features_for_fc.shape}, FC层: {self.fc}")
            traceback.print_exc()
            raise

        # 确保最终输出具有预期的形状 [batch_size, output_dim]
        # 如果output_dim为1，它可能是[batch_size] - 为了一致性保持为[batch_size, 1]？
        # 损失函数（例如MSELoss）通常处理[batch]与[batch, 1]的情况，如果目标匹配。
        # 现在保持原样，在损失计算之前进行压缩。
        return final_output


    def setup_training(self, lr=0.001, optimizer_type='Adam', scheduler_type='ReduceLROnPlateau', scheduler_params=None):
        """配置优化器和学习率调度器"""
        logging.info(f"设置训练，学习率={lr}, 优化器={optimizer_type}, 调度器={scheduler_type}")

        # 优化器
        if optimizer_type.lower() == 'adam':
            self.optimizer = torch.optim.Adam(self.parameters(), lr=lr)
        elif optimizer_type.lower() == 'sgd':
            # 如果需要，添加动量选项
            self.optimizer = torch.optim.SGD(self.parameters(), lr=lr)
        elif optimizer_type.lower() == 'rmsprop':
            self.optimizer = torch.optim.RMSprop(self.parameters(), lr=lr)
        else:
            logging.warning(f"不支持的优化器类型 '{optimizer_type}'，默认使用Adam。")
            self.optimizer = torch.optim.Adam(self.parameters(), lr=lr)
        logging.info(f"优化器已初始化: {self.optimizer}")


        # 调度器
        if scheduler_type is None:
            self.scheduler = None
            logging.info("不使用学习率调度器。")
        elif scheduler_type.lower() == 'reducelronplateau':
            # 检查PyTorch版本，较新版本不支持verbose参数
            default_scheduler_params = {'mode': 'min', 'factor': 0.5, 'patience': 10}
            # 只在较旧版本的PyTorch中添加verbose参数
            if torch.__version__.startswith(('1.8', '1.9', '1.10')):
                default_scheduler_params['verbose'] = False

            if scheduler_params:
                # 如果用户提供了scheduler_params，确保移除不兼容的参数
                if 'verbose' in scheduler_params and not torch.__version__.startswith(('1.8', '1.9', '1.10')):
                    logging.warning(f"PyTorch {torch.__version__} 的ReduceLROnPlateau不支持'verbose'参数。正在移除。")
                    scheduler_params = {k: v for k, v in scheduler_params.items() if k != 'verbose'}
                default_scheduler_params.update(scheduler_params)

            logging.info(f"使用ReduceLROnPlateau调度器，参数: {default_scheduler_params}")
            self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, **default_scheduler_params)

        elif scheduler_type.lower() == 'steplr':
            # 检查PyTorch版本，较新版本不支持verbose参数
            default_scheduler_params = {'step_size': 10, 'gamma': 0.1}
            # 只在较旧版本的PyTorch中添加verbose参数
            if torch.__version__.startswith(('1.8', '1.9', '1.10')):
                default_scheduler_params['verbose'] = False

            if scheduler_params:
                # 如果用户提供了scheduler_params，确保移除不兼容的参数
                if 'verbose' in scheduler_params and not torch.__version__.startswith(('1.8', '1.9', '1.10')):
                    logging.warning(f"PyTorch {torch.__version__} 的StepLR不支持'verbose'参数。正在移除。")
                    scheduler_params = {k: v for k, v in scheduler_params.items() if k != 'verbose'}
                default_scheduler_params.update(scheduler_params)

            logging.info(f"使用StepLR调度器，参数: {default_scheduler_params}")
            self.scheduler = torch.optim.lr_scheduler.StepLR(self.optimizer, **default_scheduler_params)
        # 如果需要，添加更多调度器（CosineAnnealingLR等）
        else:
            logging.warning(f"不支持的调度器类型 '{scheduler_type}'，不使用调度器。")
            self.scheduler = None

        if self.scheduler:
             logging.info(f"调度器已初始化: {type(self.scheduler).__name__}")

    def train_model(self, train_loader, val_loader, epochs=50, lr=0.001, device='cpu',
                    early_stopping_patience=10, criterion=None, callbacks=None):
        """
        训练模型的主循环。

        Args:
            train_loader (DataLoader): 训练数据加载器。
            val_loader (DataLoader): 验证数据加载器。
            epochs (int): 最大训练轮次。
            lr (float): 学习率。
            device (str or torch.device): 训练设备 ('cpu' or 'cuda').
            early_stopping_patience (int): 早停耐心值。设置为 0 或 None 禁用早停。
            criterion (nn.Module, optional): 损失函数。如果为 None, 默认为 nn.MSELoss。
            callbacks (list, optional): 回调对象列表。

        Returns:
            tuple: (log_history, final_val_loss, epochs_run)
                   log_history: 包含 (epoch, train_loss, val_loss) 的列表。
                   final_val_loss: 最后一轮的验证损失。
                   epochs_run: 实际运行的轮次。
        """
        print(f"在设备 '{device}' 上开始训练，共 {epochs} 轮...")
        self.to(device)

        # 设置损失函数、优化器、调度器
        criterion = criterion or nn.MSELoss()
        # 使用专用方法设置优化器和调度器
        # 使用ReduceLROnPlateau作为默认值，与TrainingThread匹配
        scheduler_patience = max(1, early_stopping_patience // 2) if early_stopping_patience else 5

        # 创建scheduler参数，根据PyTorch版本决定是否包含verbose参数
        scheduler_params = {'patience': scheduler_patience}
        # 只在较旧版本的PyTorch中添加verbose参数
        if torch.__version__.startswith(('1.8', '1.9', '1.10')):
            scheduler_params['verbose'] = False

        self.setup_training(lr=lr, optimizer_type='Adam', scheduler_type='ReduceLROnPlateau',
                            scheduler_params=scheduler_params)

        # 设置回调
        callbacks = callbacks or []
        # 确保必要的回调（如日志记录）存在
        # 示例：如果没有提供日志记录器，则添加默认日志记录器
        has_logger = any(isinstance(cb, LoggingCallback) for cb in callbacks)
        if not has_logger:
             callbacks.insert(0, LoggingCallback()) # 添加到开头

        # 为回调提供对模型和训练参数的访问
        callback_params = {'epochs': epochs, 'lr': lr, 'device': device, 'patience': early_stopping_patience}
        for cb in callbacks:
            cb.set_model(self)
            cb.set_params(callback_params)

        # 训练状态
        best_val_loss = float('inf')
        epochs_no_improve = 0
        epochs_run = 0
        log_history = [] # 存储 (epoch, train_loss, val_loss) 元组
        final_val_loss_value = float('nan') # 最后一轮运行的值

        # 回调: 训练开始
        for cb in callbacks: cb.on_train_begin(logs={'start_time': time.time()})

        # 主训练循环
        try:
            for epoch in range(epochs):
                epochs_run = epoch + 1 # 跟踪实际运行的轮次
                epoch_logs = {}

                # 回调: 轮次开始
                for cb in callbacks: cb.on_epoch_begin(epoch, logs=epoch_logs)

                # 训练阶段
                self.train() # 将模型设置为训练模式
                running_train_loss = 0.0
                train_batch_count = 0
                for i, (inputs, targets) in enumerate(train_loader):
                    batch_logs = {}
                    inputs, targets = inputs.to(device), targets.to(device)

                    # allback: on_batch_begin
                    for cb in callbacks: cb.on_batch_begin(i, logs=batch_logs)

                    self.optimizer.zero_grad()
                    outputs = self(inputs) # 前向传播

                    # 如有必要，压缩输出/目标以进行损失计算
                    if outputs.ndim > 1 and outputs.shape[1] == 1: outputs = outputs.squeeze(1)
                    if targets.ndim > 1 and targets.shape[1] == 1: targets = targets.squeeze(1)

                    try:
                        loss = criterion(outputs, targets)
                    except RuntimeError as loss_err:
                        logging.error(f"损失计算期间发生RuntimeError (轮次 {epoch+1}, 批次 {i+1}): {loss_err}")
                        logging.error(f"输出形状: {outputs.shape}, 目标形状: {targets.shape}")
                        # 选项: 跳过批次或重新抛出
                        # continue # 跳过批次
                        raise loss_err # 重新抛出以停止训练

                    loss.backward() # 反向传播
                    # 可选：梯度裁剪
                    # torch.nn.utils.clip_grad_norm_(self.parameters(), max_norm=1.0)
                    self.optimizer.step() # 更新权重

                    batch_loss = loss.item()
                    running_train_loss += batch_loss * inputs.size(0)
                    train_batch_count += inputs.size(0)

                    batch_logs['loss'] = batch_loss
                    # 回调: on_batch_end
                    for cb in callbacks: cb.on_batch_end(i, logs=batch_logs)

                epoch_train_loss = running_train_loss / train_batch_count if train_batch_count > 0 else 0.0
                epoch_logs['train_loss'] = epoch_train_loss

                # 验证阶段
                epoch_val_loss, _ , _ = self.evaluate(val_loader, criterion, device, calculate_extra_metrics=False) # 这里不需要额外的指标
                epoch_logs['val_loss'] = epoch_val_loss
                final_val_loss_value = epoch_val_loss # 每个轮次更新最终损失值

                log_history.append((epoch + 1, epoch_train_loss, epoch_val_loss))

                # 回调：轮次结束
                # 必须在依赖epoch_logs的调度器步骤和早停检查之前发生
                for cb in callbacks: cb.on_epoch_end(epoch, logs=epoch_logs)

                # 调度器步骤
                if self.scheduler:
                    if isinstance(self.scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                        self.scheduler.step(epoch_val_loss)
                    else:
                        self.scheduler.step() # 对于像StepLR这样的调度器

                # 早停
                if early_stopping_patience and early_stopping_patience > 0:
                    if epoch_val_loss < best_val_loss:
                        best_val_loss = epoch_val_loss
                        epochs_no_improve = 0
                        # 检查点回调处理保存最佳模型
                    else:
                        epochs_no_improve += 1
                        if epochs_no_improve >= early_stopping_patience:
                            logging.info(f"在轮次 {epoch + 1} 触发早停，经过 {early_stopping_patience} 轮没有改善。")
                            break # 退出训练循环

            # 训练循环结束

        except Exception as e:
             logging.error(f"训练循环在轮次 {epoch+1} 期间发生异常: {e}")
             traceback.print_exc()
             # 即使在错误时也触发train_end回调？可能带有错误状态？
             train_end_logs = {'status': 'error', 'error_message': str(e)}
             for cb in callbacks: cb.on_train_end(logs=train_end_logs)
             # 重新抛出异常以表示失败
             raise e
        finally:
             # 回调: 训练结束（如果没有异常或在处理后总是被调用）
             # 检查循环是否正常完成或提前中断
             if 'train_end_logs' not in locals(): # 没有进入异常块的finally
                train_end_logs = {'status': 'completed' if epoch == epochs - 1 else 'early_stopped'}
                train_end_logs['final_train_loss'] = epoch_logs.get('train_loss', float('nan'))
                train_end_logs['final_val_loss'] = epoch_logs.get('val_loss', float('nan'))
                train_end_logs['best_val_loss'] = best_val_loss
                train_end_logs['epochs_run'] = epochs_run
                for cb in callbacks: cb.on_train_end(logs=train_end_logs)

        logging.info(f"训练在 {epochs_run} 轮后完成。最终验证损失: {final_val_loss_value:.6f}, 最佳验证损失: {best_val_loss:.6f}")
        return log_history, final_val_loss_value, epochs_run


    def evaluate(self, loader, criterion=None, device='cpu', calculate_extra_metrics=True):
        """
        评估模型性能。

        Args:
            loader (DataLoader): 数据加载器 (通常是验证或测试集)。
            criterion (nn.Module, optional): 损失函数。如果为 None, 默认为 nn.MSELoss。
            device (str or torch.device): 评估设备。
            calculate_extra_metrics (bool): 是否计算并返回 MAE, RMSE, R2。

        Returns:
            tuple:
                - avg_loss (float): 平均损失值 (MSE或指定的准则)。
                - all_targets (np.array or None): 所有真实目标值 (如果 calculate_extra_metrics=True)。
                - all_predictions (np.array or None): 所有模型预测值 (如果 calculate_extra_metrics=True)。
                - (可选) extra_metrics (dict or None): 包含 MAE, RMSE, R2 的字典 (如果 calculate_extra_metrics=True)。
        """
        self.eval() # 将模型设置为评估模式
        self.to(device)
        criterion = criterion or nn.MSELoss()
        total_loss = 0.0
        all_targets_list = []
        all_predictions_list = []
        evaluated_samples = 0

        with torch.no_grad():
            for inputs, targets in loader:
                inputs, targets = inputs.to(device), targets.to(device)
                batch_size = inputs.size(0)

                outputs = self(inputs) # 前向传播

                # 压缩输出/目标以进行损失计算和指标比较
                if outputs.ndim > 1 and outputs.shape[1] == 1: outputs = outputs.squeeze(1)
                if targets.ndim > 1 and targets.shape[1] == 1: targets = targets.squeeze(1)

                # 计算批次的损失
                try:
                    loss = criterion(outputs, targets)
                    total_loss += loss.item() * batch_size
                    evaluated_samples += batch_size
                except RuntimeError as loss_err:
                    logging.error(f"评估损失计算期间发生RuntimeError: {loss_err}")
                    logging.error(f"输出形状: {outputs.shape}, 目标形状: {targets.shape}")
                    # 决定如何处理：跳过批次？返回NaN？抛出错误？
                    # 目前，让我们跳过这个批次对损失和指标的贡献
                    continue

                # 如果计算额外指标，存储目标和预测
                if calculate_extra_metrics:
                    all_targets_list.append(targets.cpu().numpy())
                    all_predictions_list.append(outputs.cpu().numpy())

        # 计算平均损失
        avg_loss = total_loss / evaluated_samples if evaluated_samples > 0 else float('nan')

        # 如果请求，计算额外指标
        if calculate_extra_metrics and evaluated_samples > 0:
            all_targets = np.concatenate(all_targets_list)
            all_predictions = np.concatenate(all_predictions_list)

            # 确保形状兼容（例如，如果需要则展平）
            if all_targets.ndim > 1 and all_targets.shape[1] == 1: all_targets = all_targets.flatten()
            if all_predictions.ndim > 1 and all_predictions.shape[1] == 1: all_predictions = all_predictions.flatten()

            if all_targets.shape != all_predictions.shape:
                 logging.error(f"指标计算期间目标 ({all_targets.shape}) 和预测 ({all_predictions.shape}) 之间的形状不匹配。")
                 extra_metrics = {'mae': float('nan'), 'rmse': float('nan'), 'r2': float('nan'), 'error': '形状不匹配'}
            else:
                try:
                    mae = calculate_mae(all_targets, all_predictions)
                    rmse = calculate_rmse(all_targets, all_predictions)
                    r2 = calculate_r2(all_targets, all_predictions)
                    extra_metrics = {'mae': mae, 'rmse': rmse, 'r2': r2}
                except Exception as metric_err:
                    logging.error(f"计算额外指标时出错: {metric_err}")
                    extra_metrics = {'mae': float('nan'), 'rmse': float('nan'), 'r2': float('nan'), 'error': str(metric_err)}

            return avg_loss, all_targets, all_predictions, extra_metrics
        elif calculate_extra_metrics: # 没有评估样本
             return avg_loss, np.array([]), np.array([]), {'mae': float('nan'), 'rmse': float('nan'), 'r2': float('nan')}
        else: # 不计算额外指标
            return avg_loss, None, None


    def predict(self, inputs: Union[np.ndarray, torch.Tensor], device: Union[str, torch.device] = 'cpu') -> np.ndarray:
        """
        使用模型进行预测。

        Args:
            inputs (np.ndarray or torch.Tensor): 输入数据。
                可以是单个序列 [seq_len, input_dim]
                或一个批次的序列 [batch_size, seq_len, input_dim]。
            device (str or torch.device): 预测设备。

        Returns:
            np.ndarray: 预测结果。
                如果输入是单个序列，返回 [output_dim]。
                如果输入是一个批次，返回 [batch_size, output_dim]。
        """
        self.eval() # 将模型设置为评估模式
        self.to(device)

        with torch.no_grad():
            # 1. 将输入转换为张量并移动到设备
            if isinstance(inputs, np.ndarray):
                inputs = torch.from_numpy(inputs).float()
            elif not isinstance(inputs, torch.Tensor):
                 raise TypeError(f"输入必须是NumPy数组或PyTorch张量，得到了 {type(inputs)}")

            inputs = inputs.to(device)

            # 2. 确保输入具有批次维度 [batch_size, seq_len, input_dim]
            original_ndim = inputs.ndim
            if original_ndim == 2: # 假设单个序列 [seq_len, input_dim]
                inputs = inputs.unsqueeze(0) # 添加批次维度 -> [1, seq_len, input_dim]
                logging.debug("为单个序列输入添加了批次维度。")
            elif original_ndim != 3:
                raise ValueError(f"输入张量必须具有2或3个维度 (seq_len, input_dim) 或 (batch, seq_len, input_dim)，得到了 {original_ndim} 个维度，形状为 {inputs.shape}")

            # 3. 执行前向传播
            try:
                outputs = self(inputs) # 形状 [batch_size, output_dim]
            except Exception as e:
                logging.error(f"预测前向传播期间出错: {e}")
                logging.error(f"模型的输入形状为: {inputs.shape}")
                traceback.print_exc()
                # 返回预期形状的NaN数组？
                batch_size = inputs.shape[0]
                return np.full((batch_size, self.output_dim), np.nan)


            # 4. 将输出转换为NumPy数组
            predictions_np = outputs.cpu().numpy()

            # 5. 如果原始输入是单个序列，则移除批次维度
            if original_ndim == 2:
                predictions_np = predictions_np.squeeze(0) # 移除批次维度 -> [output_dim]

            return predictions_np


    def save_model(self, file_path):
        """
        保存模型的状态字典和配置信息。

        Args:
            file_path (str): 保存文件的路径 (通常以 .pth 或 .pt 结尾)。
        """
        try:
            save_content = {
                'model_state_dict': self.state_dict(),
                'model_type': self.original_model_type, # 保存原始请求类型
                'input_dim': self.input_dim,
                'output_dim': self.output_dim,
                'config': self.config,
                'use_builtin_rnn': self.use_builtin_rnn, # 保存使用的标志
                # 添加框架版本？时间戳？
                'pytorch_version': torch.__version__,
                'save_time': time.strftime("%Y-%m-%d %H:%M:%S")
            }
            torch.save(save_content, file_path)
            logging.info(f"模型状态和配置成功保存到: {file_path}")
        except Exception as e:
            logging.error(f"保存模型到 {file_path} 时出错: {e}")
            traceback.print_exc()
            # 不在这里抛出异常，让调用代码决定如何处理保存失败

    @classmethod
    def load_model_from_file(cls, file_path, device='cpu', use_builtin_rnn_override=None):
        """
        从文件加载模型状态字典和配置，并重新创建模型实例。

        Args:
            file_path (str): 模型文件路径。
            device (str or torch.device): 加载模型的设备。
            use_builtin_rnn_override (bool, optional): 覆盖加载时使用的 use_builtin_rnn 标志。如果为 None，则使用文件中保存的设置。

        Returns:
            TimeSeriesModel: 加载并配置好的模型实例。

        Raises:
            FileNotFoundError: 如果文件不存在。
            KeyError: 如果文件内容缺少必要的键。
            Exception: 其他加载或实例化错误。
        """
        logging.info(f"尝试从 {file_path} 加载模型到设备: {device}")
        if not isinstance(device, torch.device):
            device = torch.device(device)

        try:
            checkpoint = torch.load(file_path, map_location=device)
            logging.debug("检查点文件加载成功。")

            # 提取所需信息
            model_type = checkpoint.get('model_type')
            input_dim = checkpoint.get('input_dim')
            output_dim = checkpoint.get('output_dim')
            config = checkpoint.get('config')
            # 如果提供了覆盖标志，则使用覆盖标志，否则使用保存的标志（默认为 False，如果缺失则为向后兼容）
            use_builtin_rnn_saved = checkpoint.get('use_builtin_rnn', False)
            use_builtin_rnn_to_use = use_builtin_rnn_override if use_builtin_rnn_override is not None else use_builtin_rnn_saved

            if not all([model_type, isinstance(input_dim, int), isinstance(output_dim, int), isinstance(config, dict)]):
                missing = [k for k, v in {'model_type': model_type, 'input_dim': input_dim,
                                          'output_dim': output_dim, 'config': config}.items() if v is None or not isinstance(v, (str, int, dict))]
                raise KeyError(f"检查点文件 {file_path} 缺少或具有无效的必需键: {missing}")

            logging.info(f"重新实例化模型: 类型={model_type}, 输入维度={input_dim}, 输出维度={output_dim}, 使用内置RNN={use_builtin_rnn_to_use}")
            logging.debug(f"使用加载的配置: {config}")

            # 使用加载的参数创建新的模型实例
            model = cls(
                model_type=model_type,
                input_dim=input_dim,
                output_dim=output_dim,
                config=config,
                use_builtin_rnn=use_builtin_rnn_to_use
            )
            logging.debug("模型实例已创建。")

            # 加载状态字典
            model_state_dict = checkpoint.get('model_state_dict')
            if model_state_dict is None:
                 raise KeyError(f"检查点文件 {file_path} 缺少 'model_state_dict'。")

            model.load_state_dict(model_state_dict)
            logging.debug("模型state_dict加载成功。")

            model.to(device) # 确保模型在正确的设备上
            model.eval()   # 加载后默认设置为评估模式
            logging.info(f"模型 '{model_type}' 已成功从 {file_path} 加载到设备 '{device}'。")

            # 如果版本差异很大，记录版本不匹配？
            saved_torch_version = checkpoint.get('pytorch_version')
            if saved_torch_version and saved_torch_version != torch.__version__:
                 logging.warning(f"模型是使用PyTorch版本 {saved_torch_version} 保存的，当前版本是 {torch.__version__}。可能会出现兼容性问题。")

            return model

        except FileNotFoundError:
            logging.error(f"未找到模型文件: {file_path}")
            raise
        except KeyError as e:
             logging.error(f"加载模型时出错: 检查点文件中缺少键 - {e}")
             raise
        except RuntimeError as e:
             logging.error(f"加载state_dict时出现RuntimeError（可能是架构不匹配？）: {e}")
             traceback.print_exc()
             raise
        except Exception as e:
            logging.error(f"加载模型时发生意外错误: {e}")
            traceback.print_exc()
            raise


    def get_parameter_count(self):
        """返回总参数量和可训练参数量"""
        # 已经在 __init__ 中计算，只需返回它们
        return {'total': self.total_params, 'trainable': self.trainable_params}

    def __str__(self):
        """提供模型的字符串表示"""
        core_model_str = str(self.core_model)
        # 如果太长，限制核心模型字符串长度？
        # if len(core_model_str) > 1000:
        #      core_model_str = core_model_str[:500] + "\n...\n" + core_model_str[-500:]

        return (f"TimeSeriesModel封装器:\n"
                f"  原始类型请求: {self.original_model_type}\n"
                f"  核心模型类型: {type(self.core_model).__name__}\n"
                f"  输入维度: {self.input_dim}, 输出维度: {self.output_dim}\n"
                f"  配置: {self.config}\n"
                f"  使用内置RNN标志: {self.use_builtin_rnn}\n"
                f"  总参数: {self.total_params:,}\n"
                f"  可训练参数: {self.trainable_params:,}\n"
                f"--- 核心模型结构 ---\n"
                f"{core_model_str}\n"
                f"--- 最终层 ---\n"
                f"{str(self.fc)}")

# --- 超参数优化工具的占位符 ---
class HyperparameterSearchSpace:
    """定义超参数搜索空间的占位符类"""
    def __init__(self, model_type):
        self.model_type = model_type.upper()
        self.space = self._define_space()

    def _define_space(self):
        # 定义不同模型类型的搜索范围 (示例)
        space = {
            'common': {
                'lr': {'type': 'loguniform', 'low': 1e-5, 'high': 1e-2},
                'dropout': {'type': 'uniform', 'low': 0.0, 'high': 0.5},
                # 'batch_size': {'type': 'choice', 'values': [16, 32, 64]} # 批次大小通常是固定的或单独处理
            }
        }
        if self.model_type in ['LSTM', 'GRU']:
            space[self.model_type] = {
                'hidden_dim': {'type': 'int', 'low': 16, 'high': 256, 'step': 16},
                'num_layers': {'type': 'int', 'low': 1, 'high': 4}
            }
        elif self.model_type == 'TRANSFORMER':
             space[self.model_type] = {
                'hidden_dim': {'type': 'choice', 'values': [32, 64, 128, 256]}, # 必须能被nhead整除
                'num_layers': {'type': 'int', 'low': 1, 'high': 6},
                'nhead': {'type': 'choice', 'values': [2, 4, 8]} # 确保hidden_dim % nhead == 0
             }
        elif self.model_type == 'TCN':
             space[self.model_type] = {
                 # 定义列表搜索空间比较复杂，可能需要简化占位符
                 # 例如，层数和每层的通道大小
                 'tcn_num_layers': {'type': 'int', 'low': 2, 'high': 6},
                 'tcn_base_channels': {'type': 'choice', 'values': [16, 24, 32]},
                 'tcn_kernel_size': {'type': 'choice', 'values': [3, 5, 7]},
             }
        return space

    def sample_config(self):
        """从定义的空间中采样一个配置 (非常基础的随机采样示例)"""
        import random
        sampled_config = {}

        # 采样通用参数
        common_space = self.space.get('common', {})
        for name, spec in common_space.items():
            if spec['type'] == 'loguniform':
                sampled_config[name] = 10**random.uniform(math.log10(spec['low']), math.log10(spec['high']))
            elif spec['type'] == 'uniform':
                sampled_config[name] = random.uniform(spec['low'], spec['high'])
            elif spec['type'] == 'choice':
                 sampled_config[name] = random.choice(spec['values'])
            elif spec['type'] == 'int':
                 sampled_config[name] = random.randint(spec['low'], spec['high'])
                 if 'step' in spec:
                     sampled_config[name] = (sampled_config[name] // spec['step']) * spec['step']

        # 采样模型特定参数
        model_space = self.space.get(self.model_type, {})
        for name, spec in model_space.items():
             # 简化的占位符采样
            if spec['type'] == 'choice':
                 sampled_config[name] = random.choice(spec['values'])
            elif spec['type'] == 'int':
                 sampled_config[name] = random.randint(spec['low'], spec['high'])
                 if 'step' in spec:
                     sampled_config[name] = (sampled_config[name] // spec['step']) * spec['step']
            # 根据层数/基础通道处理TCN通道生成
            if name == 'tcn_num_layers': # 假设在tcn_base_channels之前采样
                num_layers = sampled_config[name]
                base_channels = sampled_config.get('tcn_base_channels', 32) # 使用采样值或默认值
                # 示例：通道增长，例如[32, 32, 32]或[32, 64, 128] - 简单版本：恒定通道
                sampled_config['tcn_num_channels'] = [base_channels] * num_layers
                # 如果存在，删除辅助键
                if 'tcn_num_layers' in sampled_config: del sampled_config['tcn_num_layers']
                if 'tcn_base_channels' in sampled_config: del sampled_config['tcn_base_channels']


        # 后处理/验证
        # 确保Transformer的hidden_dim与nhead兼容
        if self.model_type == 'TRANSFORMER':
             if 'hidden_dim' in sampled_config and 'nhead' in sampled_config:
                 h_dim = sampled_config['hidden_dim']
                 n_h = sampled_config['nhead']
                 if h_dim % n_h != 0:
                     # 找到最近的有效hidden_dim或调整nhead？简单地调整hidden_dim。
                     # 从选项中找到n_h的最小倍数 >= h_dim
                     valid_dims = [d for d in self.space[self.model_type]['hidden_dim']['values'] if d % n_h == 0]
                     if valid_dims:
                         # 找到第一个有效的dim >= 采样的h_dim，或者如果没有 >= 的，则找最大的有效dim
                         chosen_dim = min([d for d in valid_dims if d >= h_dim] or [max(valid_dims)])
                         logging.debug(f"调整Transformer的hidden_dim从 {h_dim} 到 {chosen_dim}，以便能被nhead={n_h}整除")
                         sampled_config['hidden_dim'] = chosen_dim
                     else:
                         # 这种情况在示例空间中不应该发生，但防御性处理
                         logging.warning(f"找不到能被nhead={n_h}整除的有效hidden_dim。保留原始值 {h_dim}。")

        logging.info(f"采样的配置: {sampled_config}")
        return sampled_config


# __main__ 测试部分
if __name__ == "__main__":
    # 为独立运行设置基本日志记录
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    print("=" * 50)
    print("   运行models.py独立测试   ")
    print("=" * 50)

    # 测试数据设置
    SEQ_LENGTH = 24
    INPUT_DIM = 10
    OUTPUT_DIM = 1
    N_SAMPLES = 1000
    BATCH_SIZE = 32
    TEST_EPOCHS = 3 # 保持轮次较少以便快速测试
    EARLY_STOPPING = 2

    print("\n--- 生成测试数据 ---")
    try:
        # 生成更真实的模拟数据
        time_index = pd.date_range(start='2023-01-01', periods=N_SAMPLES, freq='H')
        X_dummy = np.random.randn(N_SAMPLES, INPUT_DIM).astype(np.float32)
        # 使目标在一定程度上依赖于第一个输入特征和时间
        signal = np.sin(np.arange(N_SAMPLES) * 2 * np.pi / (24 * 7)) * 5 # 每周季节性
        noise = np.random.randn(N_SAMPLES) * 0.5
        y_dummy = X_dummy[:, 0] * 0.6 + X_dummy[:, 1] * 0.3 + signal + 10 + noise
        y_dummy = y_dummy.astype(np.float32).reshape(-1, OUTPUT_DIM)
        print(f"生成的X_dummy形状: {X_dummy.shape}, y_dummy形状: {y_dummy.shape}")

        # 创建序列
        num_sequences = N_SAMPLES - SEQ_LENGTH
        if num_sequences <= BATCH_SIZE * 2 : # 需要足够的样本至少形成一个训练批次和一个验证批次
            raise ValueError(f"样本数量不足 ({N_SAMPLES}) 无法创建足够的序列 "
                             f"(长度 {SEQ_LENGTH}) 用于训练/验证分割，批次大小为 {BATCH_SIZE}。")
        X_seq = np.array([X_dummy[i : i + SEQ_LENGTH] for i in range(num_sequences)])
        y_seq = np.array([y_dummy[i + SEQ_LENGTH] for i in range(num_sequences)])
        print(f"创建的序列 X_seq 形状: {X_seq.shape}, y_seq 形状: {y_seq.shape}")

        # 分割数据
        split_ratio = 0.8
        split_idx = int(split_ratio * num_sequences)
        if split_idx == 0 or split_idx >= num_sequences:
             raise ValueError(f"无效的分割索引 {split_idx}，序列总数为 {num_sequences}。")

        X_train, X_val = X_seq[:split_idx], X_seq[split_idx:]
        y_train, y_val = y_seq[:split_idx], y_seq[split_idx:]
        print(f"训练集形状: X={X_train.shape}, y={y_train.shape}")
        print(f"验证集形状: X={X_val.shape}, y={y_val.shape}")

        # 创建数据加载器
        train_dataset = TensorDataset(torch.FloatTensor(X_train), torch.FloatTensor(y_train))
        val_dataset = TensorDataset(torch.FloatTensor(X_val), torch.FloatTensor(y_val))
        train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=0) # num_workers=0 简化处理
        val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE * 2, shuffle=False, num_workers=0)
        print("数据加载器已创建。")
    except ValueError as e:
        print(f"测试数据设置过程中出错: {e}")
        # 如果数据设置失败则退出
        exit(1)
    except Exception as e:
        print(f"测试数据设置过程中出现意外错误: {e}")
        traceback.print_exc()
        exit(1)

    # Device选择和初始化
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"\n--- 使用设备: {device} ---")

    # 模型测试循环
    # 测试内置和自定义（如果可用）的RNN
    test_configurations = [
        {'type': 'LSTM', 'use_builtin': True, 'config': {'hidden_dim': 32, 'num_layers': 1, 'dropout': 0.0}},
        {'type': 'GRU', 'use_builtin': True, 'config': {'hidden_dim': 48, 'num_layers': 2, 'dropout': 0.1}},
    ]
    if MODEL_ARCHITECTURES_AVAILABLE:
         test_configurations.extend([
            {'type': 'LSTM', 'use_builtin': False, 'config': {'hidden_dim': 32, 'num_layers': 1, 'dropout': 0.0}},
            {'type': 'GRU', 'use_builtin': False, 'config': {'hidden_dim': 48, 'num_layers': 2, 'dropout': 0.1}},
            {'type': 'Transformer', 'use_builtin': False, 'config': {'hidden_dim': 64, 'num_layers': 2, 'nhead': 4, 'dropout': 0.15}},
            {'type': 'TCN', 'use_builtin': False, 'config': {'tcn_num_channels': [24, 48], 'tcn_kernel_size': 3, 'dropout': 0.1}},
         ])
    else:
        print("\n自定义架构不可用，跳过自定义RNN、Transformer和TCN的测试。")


    results_summary = []

    for test_case in test_configurations:
        model_type = test_case['type']
        use_builtin = test_case['use_builtin']
        custom_config = test_case['config']
        test_name = f"{model_type}{' (内置)' if use_builtin and model_type in ['LSTM', 'GRU'] else (' (自定义)' if not use_builtin and model_type in ['LSTM', 'GRU'] else '')}"

        print(f"\n{'='*20} 测试: {test_name} {'='*20}")
        print(f"Config: {custom_config}")

        try:
            # 1. 实例化模型
            model_instance = TimeSeriesModel(
                model_type=model_type,
                input_dim=INPUT_DIM,
                output_dim=OUTPUT_DIM,
                config=custom_config,
                use_builtin_rnn=use_builtin # 传递标志
            )
            print(f"模型结构:\n{model_instance}") # 打印详细结构
            param_count = model_instance.get_parameter_count()
            print(f"参数: 总计={param_count['total']:,}, 可训练={param_count['trainable']:,}")


            # 2. 设置测试回调
            checkpoint_path = f"temp_best_{model_type}_{'builtin' if use_builtin else 'custom'}.pth"
            callbacks = [
                LoggingCallback(), # 使用详细的日志记录回调
                SimpleModelCheckpoint(filepath=checkpoint_path, monitor='val_loss', verbose=1)
            ]

            # 3. 训练模型
            start_train_time = time.time()
            log_history, final_val_loss, epochs_run = model_instance.train_model(
                train_loader, val_loader, epochs=TEST_EPOCHS, lr=0.005, device=device,
                early_stopping_patience=EARLY_STOPPING, callbacks=callbacks
            )
            train_duration = time.time() - start_train_time
            print(f"训练在 {train_duration:.2f} 秒内完成，共 {epochs_run} 个轮次。")

            # 4. 评估模型（使用最后一个轮次后的状态）
            print("正在评估最终轮次后的模型...")
            eval_loss, targets_eval, preds_eval, metrics_eval = model_instance.evaluate(
                val_loader, nn.MSELoss(), device=device, calculate_extra_metrics=True
            )
            print(f"评估结果（最终模型状态）:")
            print(f"  MSE损失: {eval_loss:.6f}")
            if metrics_eval:
                 print(f"  MAE: {metrics_eval.get('mae', 'N/A'):.6f}")
                 print(f"  RMSE: {metrics_eval.get('rmse', 'N/A'):.6f}")
                 print(f"  R2: {metrics_eval.get('r2', 'N/A'):.4f}")
                 if metrics_eval.get('error'): print(f"  指标错误: {metrics_eval['error']}")


            # 5. 测试预测
            # 使用验证集中的一个批次进行预测测试
            sample_input_batch_t, sample_target_batch_t = next(iter(val_loader))
            sample_input_batch_np = sample_input_batch_t.cpu().numpy() # 预测期望numpy或张量
            predictions = model_instance.predict(sample_input_batch_np, device=device)
            print(f"预测测试在批次大小 {sample_input_batch_np.shape[0]}:")
            print(f"  输入形状: {sample_input_batch_np.shape}")
            print(f"  输出预测形状: {predictions.shape}")
            # 打印前几个预测vs目标
            for k in range(min(3, len(predictions))):
                 print(f"  样本 {k}: 预测值={predictions[k][0]:.4f}, 目标值={sample_target_batch_t[k].item():.4f}")


            # 6. 测试保存和加载
            if os.path.exists(checkpoint_path):
                 print(f"从最佳检查点加载模型: {checkpoint_path}")
                 loaded_model = TimeSeriesModel.load_model_from_file(
                     checkpoint_path, device=device,
                     # 确保覆盖与模型创建方式匹配（如果需要），
                     # 但加载保存的标志在这里应该是正确的。
                     use_builtin_rnn_override=use_builtin
                 )
                 print(f"模型已成功从检查点加载。")

                 # 评估加载的模型
                 print("正在评估加载的最佳模型...")
                 eval_loss_loaded, _, _, metrics_loaded = loaded_model.evaluate(
                     val_loader, nn.MSELoss(), device=device, calculate_extra_metrics=True
                 )
                 print(f"评估结果（加载的最佳模型）:")
                 print(f"  MSE损失: {eval_loss_loaded:.6f}")
                 if metrics_loaded:
                     print(f"  MAE: {metrics_loaded.get('mae', 'N/A'):.6f}")
                     print(f"  RMSE: {metrics_loaded.get('rmse', 'N/A'):.6f}")
                     print(f"  R2: {metrics_loaded.get('r2', 'N/A'):.4f}")

                 # 比较加载后的预测
                 prediction_after_load = loaded_model.predict(sample_input_batch_np, device=device)
                 # 注意：比较*最佳*加载模型与*最终*模型状态的预测并不直接。
                 # 我们需要保存最终状态以进行直接比较。
                 # 让我们只检查预测是否运行无误。
                 print(f"加载模型的预测成功运行，输出形状: {prediction_after_load.shape}")

                 # 清理检查点
                 os.remove(checkpoint_path)
                 print(f"已删除临时检查点文件: {checkpoint_path}")

            else:
                 print(f"跳过加载测试，因为检查点文件 '{checkpoint_path}' 未创建（可能没有改进）。")
                 # 将加载的指标设置为NaN以便汇总
                 eval_loss_loaded = float('nan')
                 metrics_loaded = {'mae': float('nan'), 'rmse': float('nan'), 'r2': float('nan')}


            # 存储结果
            results_summary.append({
                'Test Name': test_name,
                'Epochs Run': epochs_run,
                'Train Duration (s)': train_duration,
                'Final Val MSE': final_val_loss,
                'Eval MSE (Final)': eval_loss,
                'Eval MAE (Final)': metrics_eval.get('mae', float('nan')),
                'Eval R2 (Final)': metrics_eval.get('r2', float('nan')),
                'Eval MSE (Best Ckpt)': eval_loss_loaded,
                'Eval MAE (Best Ckpt)': metrics_loaded.get('mae', float('nan')),
                 'Eval R2 (Best Ckpt)': metrics_loaded.get('r2', float('nan')),
                'Params (Trainable)': param_count['trainable'],
            })

        except Exception as e:
            print(f"!!!!!!!!!! ERROR testing {test_name}: {e} !!!!!!!!!!")
            traceback.print_exc()
            results_summary.append({
                'Test Name': test_name, 'Epochs Run': 'ERROR', 'Train Duration (s)': 'ERROR',
                'Final Val MSE': 'ERROR', 'Eval MSE (Final)': 'ERROR', 'Eval MAE (Final)': 'ERROR',
                'Eval R2 (Final)': 'ERROR', 'Eval MSE (Best Ckpt)': 'ERROR', 'Eval MAE (Best Ckpt)': 'ERROR',
                'Eval R2 (Best Ckpt)': 'ERROR', 'Params (Trainable)': 'ERROR'
            })

        print(f"{'='*20} 完成: {test_name} {'='*20}")


    # 打印摘要表
    print("\n\n" + "=" * 60)
    print("              独立测试结果摘要")
    print("=" * 60)
    if results_summary:
        # 使用pandas进行美观格式化（如果可用）
        try:
            import pandas as pd
            summary_df = pd.DataFrame(results_summary)
            # 格式化浮点数
            float_cols = [col for col in summary_df.columns if 'MSE' in col or 'MAE' in col or 'R2' in col or 'Duration' in col]
            for col in float_cols:
                 summary_df[col] = summary_df[col].apply(lambda x: f"{x:.4f}" if isinstance(x, (float, np.float32, np.float64)) and not np.isnan(x) else x)
            param_cols = [col for col in summary_df.columns if 'Params' in col]
            for col in param_cols:
                 summary_df[col] = summary_df[col].apply(lambda x: f"{x:,}" if isinstance(x, int) else x)

            print(summary_df.to_string(index=False))
        except ImportError:
            # 回退到基本打印
            headers = results_summary[0].keys()
            print(" | ".join(headers))
            print("-" * (len(" | ".join(headers)) + len(headers)*2))
            for result in results_summary:
                print(" | ".join(map(str, result.values())))
    else:
        print("没有测试结果可显示。")

    print("=" * 60)
    print("models.py独立测试完成。")
    print("=" * 60)
# 4.2.1 系统总体架构设计

## 4.2.1.1 架构设计原则

本系统采用分层架构设计模式，遵循以下设计原则：

1. **分层解耦**：将系统分为表示层、业务逻辑层、数据访问层，各层职责明确，降低耦合度
2. **模块化设计**：各功能模块独立开发，便于维护和扩展
3. **可扩展性**：支持新增模型类型和数据类型，具备良好的扩展能力
4. **可复用性**：核心组件可在不同场景下复用
5. **易维护性**：代码结构清晰，便于后期维护和升级

## 4.2.1.2 系统整体架构

系统采用三层架构模式，从上到下分为：

### 表示层（Presentation Layer）
- **主要组件**：WeatherAnalyzerApp（主窗口类）
- **职责**：
  - 用户界面展示和交互
  - 用户输入验证和处理
  - 结果可视化展示
  - 系统状态反馈

### 业务逻辑层（Business Logic Layer）
- **主要组件**：
  - TimeSeriesModel（模型封装器）
  - TrainingThread（训练线程）
  - 数据处理模块（data.py）
  - 模型架构模块（model_architectures/）
- **职责**：
  - 核心业务逻辑处理
  - 模型训练和预测
  - 数据预处理和特征工程
  - 算法实现和优化

### 数据访问层（Data Access Layer）
- **主要组件**：DatabaseManager（DB.py）
- **职责**：
  - 数据库操作封装
  - 模型持久化存储
  - 训练记录管理
  - 数据一致性保证

**【插入图1：系统三层架构图】**
*说明：展示表示层、业务逻辑层、数据访问层的关系和数据流向*

## 4.2.1.3 核心模块架构

### 模型管理架构
```
TimeSeriesModel (统一接口)
├── LSTMModel (LSTM实现)
├── GRUModel (GRU实现)
├── TransformerModel (Transformer实现)
├── TCNModel (TCN实现)
└── HybridLSTMAttentionModel (混合模型)
```

### 数据处理架构
```
数据处理流水线
├── 数据加载 (load_time_series_data)
├── 数据预处理 (preprocess_time_series)
├── 特征工程 (create_time_series_features)
└── 数据归一化 (normalize_time_series)
```

**【插入图2：核心模块架构图】**
*说明：展示各核心模块的组织结构和依赖关系*

## 4.2.1.4 数据流架构

系统数据流分为以下几个阶段：

1. **数据输入阶段**：用户通过UI选择数据文件，系统加载原始数据
2. **数据处理阶段**：对原始数据进行清洗、特征工程、归一化处理
3. **模型训练阶段**：使用处理后的数据训练深度学习模型
4. **模型评估阶段**：对训练好的模型进行性能评估
5. **预测应用阶段**：使用模型进行单步或多步预测
6. **结果展示阶段**：将预测结果和评估指标可视化展示

**【插入图3：系统数据流图】**
*说明：展示数据在系统中的流转过程和处理步骤*

## 4.2.1.5 技术架构选型

### 核心技术栈
- **深度学习框架**：PyTorch 1.9+
- **用户界面框架**：PyQt6
- **数据处理库**：pandas, numpy
- **可视化库**：matplotlib
- **数据库**：SQLite
- **开发语言**：Python 3.9+

### 架构优势
1. **跨平台兼容**：基于Python和PyQt6，支持Windows、Linux、macOS
2. **轻量级部署**：使用SQLite数据库，无需额外数据库服务
3. **模块化设计**：各模块职责清晰，便于团队协作开发
4. **扩展性强**：支持新增模型类型和数据处理方式

**【插入图4：技术架构图】**
*说明：展示系统使用的技术栈和组件关系*

# 4.2.5 模块详细设计

## 4.2.5.1 用户界面模块设计

### WeatherAnalyzerApp类设计
```python
class WeatherAnalyzerApp(QMainWindow):
    """主应用程序窗口类"""
    
    # 核心属性
    - data: DataFrame                    # 归一化数据
    - scaler: MinMaxScaler              # 数据缩放器
    - model: TimeSeriesModel            # 当前模型实例
    - db_manager: DatabaseManager       # 数据库管理器
    - training_thread: TrainingThread   # 训练线程
    
    # 主要方法
    + init_ui()                         # 初始化用户界面
    + load_data()                       # 加载数据文件
    + start_training()                  # 开始模型训练
    + make_prediction()                 # 执行预测
    + update_plots()                    # 更新图表显示
```

### 界面组件设计
- **数据管理区域**：文件选择、数据类型选择、目标列选择
- **模型配置区域**：模型类型选择、超参数设置
- **训练控制区域**：训练开始/停止、进度显示
- **结果展示区域**：图表显示、评估指标、预测结果
- **模型管理区域**：模型保存/加载、历史记录查看

**【插入图5：用户界面模块结构图】**
*说明：展示UI模块的组件布局和交互关系*

## 4.2.5.2 数据处理模块设计

### 数据加载子模块
```python
def load_time_series_data(file_path, data_type, custom_mapping=None):
    """通用时序数据加载函数"""
    # 支持的数据类型：WEATHER, STOCK, ENERGY, CUSTOM
    # 自动检测时间列和数值列
    # 返回标准化的DataFrame
```

### 数据预处理子模块
```python
def preprocess_time_series(df, data_type, custom_filters=None):
    """通用时序数据预处理函数"""
    # 数据清洗：缺失值处理、异常值检测
    # 噪声过滤：中值滤波
    # 数据验证：完整性检查
```

### 特征工程子模块
```python
def create_time_series_features(df, data_type, custom_config=None):
    """时序特征工程函数"""
    # 时间特征：小时、日期、星期、月份的周期性编码
    # 滞后特征：历史值特征
    # 统计特征：滑动窗口统计
    # 领域特征：根据数据类型生成专门特征
```

**【插入图6：数据处理模块流程图】**
*说明：展示数据处理的详细流程和各子模块的作用*

## 4.2.5.3 模型管理模块设计

### TimeSeriesModel统一接口
```python
class TimeSeriesModel(nn.Module):
    """时序模型统一封装器"""
    
    # 支持的模型类型
    SUPPORTED_TYPES = ['LSTM', 'GRU', 'TRANSFORMER', 'TCN', 'HYBRID']
    
    # 核心方法
    + __init__(model_type, input_dim, output_dim, config)
    + forward(x)                        # 前向传播
    + train_model(train_loader, val_loader, config)  # 模型训练
    + predict(x)                        # 预测
    + save_model(file_path)             # 模型保存
    + load_model(file_path)             # 模型加载
```

### 具体模型架构
每个具体模型实现都遵循统一接口：

1. **LSTMModel**：长短期记忆网络实现
2. **GRUModel**：门控循环单元实现  
3. **TransformerModel**：Transformer架构实现
4. **TCNModel**：时间卷积网络实现
5. **HybridLSTMAttentionModel**：LSTM+注意力机制混合模型

**【插入图7：模型架构类图】**
*说明：展示模型类的继承关系和接口设计*

## 4.2.5.4 数据库管理模块设计

### DatabaseManager类设计
```python
class DatabaseManager:
    """数据库管理器"""
    
    # 数据表设计
    - models表：存储模型基本信息
    - training_logs表：存储训练过程日志
    - training_summary表：存储训练摘要信息
    
    # 核心方法
    + save_model_info()                 # 保存模型信息
    + get_model_list()                  # 获取模型列表
    + save_training_log()               # 保存训练日志
    + get_training_logs()               # 获取训练日志
    + delete_model()                    # 删除模型记录
```

### 数据库表结构设计
```sql
-- 模型信息表
CREATE TABLE models (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_type TEXT NOT NULL,
    train_end_time DATETIME NOT NULL,
    epochs_requested INTEGER,
    epochs_run INTEGER,
    seq_length INTEGER,
    config_json TEXT,
    final_val_loss REAL,
    input_dim INTEGER
);

-- 训练日志表
CREATE TABLE training_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_id INTEGER NOT NULL,
    epoch INTEGER NOT NULL,
    train_loss REAL,
    val_loss REAL,
    FOREIGN KEY(model_id) REFERENCES models(id)
);
```

**【插入图8：数据库ER图】**
*说明：展示数据库表结构和关系*

## 4.2.5.5 训练管理模块设计

### TrainingThread类设计
```python
class TrainingThread(QThread):
    """异步训练线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, float, float, float)  # 进度更新
    training_finished = pyqtSignal(int, float, float)       # 训练完成
    training_error = pyqtSignal(str)                        # 训练错误
    
    # 核心方法
    + run()                             # 线程执行函数
    + stop_training()                   # 停止训练
```

### 训练流程设计
1. **数据准备**：创建训练和验证数据加载器
2. **模型初始化**：根据配置创建模型实例
3. **训练循环**：执行前向传播、反向传播、参数更新
4. **验证评估**：在验证集上评估模型性能
5. **早停机制**：防止过拟合，提高训练效率
6. **结果保存**：保存最佳模型和训练记录

**【插入图9：训练流程图】**
*说明：展示模型训练的详细流程和控制逻辑*

## 4.2.5.6 模块间交互设计

### 主要交互流程
1. **数据加载流程**：UI → 数据处理模块 → 数据库模块
2. **模型训练流程**：UI → 训练模块 → 模型模块 → 数据库模块
3. **预测流程**：UI → 模型模块 → 数据处理模块
4. **结果展示流程**：各模块 → UI模块

### 模块依赖关系
- UI模块依赖所有其他模块
- 训练模块依赖模型模块和数据处理模块
- 模型模块依赖数据处理模块
- 数据库模块相对独立，被其他模块调用

**【插入图10：模块交互序列图】**
*说明：展示典型操作场景下各模块的交互时序*

## 需要插入的图表总结

1. **图1：系统三层架构图** - 展示整体架构分层
2. **图2：核心模块架构图** - 展示模块组织结构
3. **图3：系统数据流图** - 展示数据流转过程
4. **图4：技术架构图** - 展示技术栈组成
5. **图5：用户界面模块结构图** - 展示UI组件布局
6. **图6：数据处理模块流程图** - 展示数据处理流程
7. **图7：模型架构类图** - 展示模型类设计
8. **图8：数据库ER图** - 展示数据库设计
9. **图9：训练流程图** - 展示训练过程
10. **图10：模块交互序列图** - 展示模块交互时序

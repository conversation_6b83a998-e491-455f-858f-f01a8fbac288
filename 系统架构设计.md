# 4.2.1 系统总体架构设计

## 4.2.1.1 架构设计原则

系统基于分层架构模式构建，遵循以下设计原则：

1. **分层解耦**：系统划分为表示层、业务逻辑层、数据访问层三个层次，各层职责清晰，降低模块间耦合
2. **模块化设计**：功能模块独立开发，便于后续维护和功能扩展
3. **可扩展性**：预留接口支持新增模型算法和数据类型
4. **可复用性**：核心组件设计考虑多场景复用需求
5. **易维护性**：代码结构层次分明，注释完整，便于维护升级

## 4.2.1.2 系统整体架构

系统采用经典三层架构模式，各层功能如下：

### 表示层
- **核心组件**：WeatherAnalyzerApp主窗口类
- **主要功能**：
  - 用户界面展示与交互处理
  - 输入数据验证与格式检查
  - 训练结果可视化展示
  - 系统运行状态实时反馈

### 业务逻辑层
- **核心组件**：
  - TimeSeriesModel模型封装器
  - TrainingThread异步训练线程
  - data.py数据处理模块
  - model_architectures模型架构包
- **主要功能**：
  - 时序数据预处理与特征工程
  - 深度学习模型训练与预测
  - 算法逻辑实现与性能优化
  - 业务流程控制与异常处理

### 数据访问层
- **核心组件**：DatabaseManager数据库管理器
- **主要功能**：
  - SQLite数据库操作封装
  - 模型参数持久化存储
  - 训练历史记录管理
  - 数据完整性与一致性维护

**【图4-1：系统三层架构图】**

## 4.2.1.3 核心模块架构

### 模型管理架构
系统采用统一接口设计，支持多种深度学习模型：
```
TimeSeriesModel (统一封装接口)
├── LSTMModel (长短期记忆网络)
├── GRUModel (门控循环单元)
├── TransformerModel (注意力机制模型)
├── TCNModel (时间卷积网络)
└── HybridLSTMAttentionModel (LSTM注意力混合模型)
```

### 数据处理架构
数据处理采用流水线设计，包含四个主要环节：
```
数据处理流水线
├── 数据加载模块 (load_time_series_data)
├── 数据预处理模块 (preprocess_time_series)
├── 特征工程模块 (create_time_series_features)
└── 数据归一化模块 (normalize_time_series)
```

**【图4-2：核心模块架构图】**

## 4.2.1.4 数据流架构

系统数据处理流程包含六个关键阶段：

1. **数据输入阶段**：用户选择数据文件，系统执行文件加载与格式验证
2. **数据处理阶段**：执行数据清洗、异常值处理、特征工程、归一化等预处理操作
3. **模型训练阶段**：基于处理后数据进行深度学习模型训练
4. **模型评估阶段**：使用验证集评估模型性能，计算各项评估指标
5. **预测应用阶段**：利用训练完成的模型执行单步预测或多步递归预测
6. **结果展示阶段**：通过图表形式展示预测结果、评估指标和训练过程

**【图4-3：系统数据流图】**

## 4.2.1.5 技术架构选型

### 核心技术栈
- **深度学习框架**：PyTorch 1.9+
- **用户界面框架**：PyQt6
- **数据处理库**：pandas, numpy
- **可视化库**：matplotlib
- **数据库**：SQLite
- **开发语言**：Python 3.9+

### 技术选型优势
1. **跨平台兼容性**：基于Python生态，支持Windows、Linux、macOS多平台运行
2. **轻量化部署**：采用SQLite嵌入式数据库，降低部署复杂度
3. **开发效率高**：成熟的第三方库支持，缩短开发周期
4. **社区支持完善**：技术栈成熟稳定，问题解决资源丰富

**【图4-4：技术架构图】**

# 4.2.5 模块详细设计

## 4.2.5.1 用户界面模块设计

### WeatherAnalyzerApp类设计
主窗口类负责整个应用程序的界面管理和用户交互：
```python
class WeatherAnalyzerApp(QMainWindow):
    """主应用程序窗口类"""

    # 核心属性
    - data: DataFrame                    # 存储归一化后的数据
    - scaler: MinMaxScaler              # 数据缩放器对象
    - model: TimeSeriesModel            # 当前活动模型实例
    - db_manager: DatabaseManager       # 数据库管理器
    - training_thread: TrainingThread   # 异步训练线程

    # 核心方法
    + init_ui()                         # 界面初始化与布局
    + load_data()                       # 数据文件加载处理
    + start_training()                  # 启动模型训练流程
    + make_prediction()                 # 执行预测计算
    + update_plots()                    # 刷新图表显示
```

### 界面功能区域划分
- **数据管理区域**：数据文件选择、数据类型配置、目标列设定
- **模型配置区域**：算法类型选择、网络参数调整、训练参数设置
- **训练控制区域**：训练启动停止、进度条显示、状态信息反馈
- **结果展示区域**：训练曲线图表、评估指标展示、预测结果可视化
- **模型管理区域**：模型文件保存加载、训练历史记录查看

**【图4-5：用户界面模块结构图】**

## 4.2.5.2 数据处理模块设计

### 数据加载子模块
负责从文件系统读取时序数据并进行初步格式化：
```python
def load_time_series_data(file_path, data_type, custom_mapping=None):
    """通用时序数据加载函数"""
    # 支持数据类型：WEATHER, STOCK, ENERGY, CUSTOM
    # 执行时间列自动识别与数值列检测
    # 输出标准格式DataFrame对象
```

### 数据预处理子模块
对原始数据执行清洗和质量控制操作：
```python
def preprocess_time_series(df, data_type, custom_filters=None):
    """通用时序数据预处理函数"""
    # 数据清洗：缺失值填充、异常值识别与处理
    # 噪声过滤：中值滤波算法应用
    # 数据验证：完整性检查与格式校验
```

### 特征工程子模块
基于时序数据特点构建预测特征：
```python
def create_time_series_features(df, data_type, custom_config=None):
    """时序特征工程函数"""
    # 时间特征：小时、日期、星期、月份周期性编码
    # 滞后特征：历史时间点数值特征
    # 统计特征：滑动窗口均值、方差等统计量
    # 领域特征：针对特定数据类型的专业特征
```

**【图4-6：数据处理模块流程图】**

## 4.2.5.3 模型管理模块设计

### TimeSeriesModel统一接口
提供统一的模型封装接口，支持多种深度学习算法：
```python
class TimeSeriesModel(nn.Module):
    """时序模型统一封装器"""

    # 支持的算法类型
    SUPPORTED_TYPES = ['LSTM', 'GRU', 'TRANSFORMER', 'TCN', 'HYBRID']

    # 核心接口方法
    + __init__(model_type, input_dim, output_dim, config)
    + forward(x)                        # 前向传播计算
    + train_model(train_loader, val_loader, config)  # 模型训练执行
    + predict(x)                        # 预测结果生成
    + save_model(file_path)             # 模型参数保存
    + load_model(file_path)             # 模型参数加载
```

### 具体算法实现
系统实现五种主流时序预测算法，均遵循统一接口规范：

1. **LSTMModel**：长短期记忆网络，适用于长期依赖建模
2. **GRUModel**：门控循环单元，计算效率较LSTM更高
3. **TransformerModel**：基于注意力机制，并行计算能力强
4. **TCNModel**：时间卷积网络，具备良好的感受野特性
5. **HybridLSTMAttentionModel**：LSTM与注意力机制结合的混合架构

**【图4-7：模型架构类图】**

## 4.2.5.4 数据库管理模块设计

### DatabaseManager类设计
负责系统数据的持久化存储和管理：
```python
class DatabaseManager:
    """数据库管理器"""

    # 数据表结构
    - models表：模型基本信息存储
    - training_logs表：训练过程详细记录
    - training_summary表：训练结果摘要信息

    # 核心操作方法
    + save_model_info()                 # 模型信息持久化
    + get_model_list()                  # 模型列表查询
    + save_training_log()               # 训练日志记录
    + get_training_logs()               # 训练历史查询
    + delete_model()                    # 模型记录删除
```

### 数据库表结构设计
采用SQLite数据库，设计三个核心数据表：
```sql
-- 模型基本信息表
CREATE TABLE models (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_type TEXT NOT NULL,
    train_end_time DATETIME NOT NULL,
    epochs_requested INTEGER,
    epochs_run INTEGER,
    seq_length INTEGER,
    config_json TEXT,
    final_val_loss REAL,
    input_dim INTEGER
);

-- 训练过程日志表
CREATE TABLE training_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_id INTEGER NOT NULL,
    epoch INTEGER NOT NULL,
    train_loss REAL,
    val_loss REAL,
    FOREIGN KEY(model_id) REFERENCES models(id)
);
```

**【图4-8：数据库ER图】**

## 4.2.5.5 训练管理模块设计

### TrainingThread类设计
采用多线程技术实现异步训练，避免界面冻结：
```python
class TrainingThread(QThread):
    """异步训练线程"""

    # Qt信号定义
    progress_updated = pyqtSignal(int, float, float, float)  # 训练进度更新
    training_finished = pyqtSignal(int, float, float)       # 训练完成通知
    training_error = pyqtSignal(str)                        # 训练异常报告

    # 线程控制方法
    + run()                             # 线程主执行函数
    + stop_training()                   # 训练中断控制
```

### 训练执行流程
训练过程包含六个关键步骤：
1. **数据准备阶段**：构建训练集和验证集数据加载器
2. **模型初始化阶段**：根据用户配置创建对应算法模型实例
3. **训练循环阶段**：执行前向传播、损失计算、反向传播、参数更新
4. **验证评估阶段**：使用验证集评估当前模型性能
5. **早停判断阶段**：监控验证损失变化，防止过拟合现象
6. **结果保存阶段**：持久化最优模型参数和训练记录

**【图4-9：训练流程图】**

## 4.2.5.6 模块间交互设计

### 核心交互流程
系统运行过程中的四个主要交互流程：
1. **数据加载流程**：用户界面模块 → 数据处理模块 → 数据库模块
2. **模型训练流程**：用户界面模块 → 训练管理模块 → 模型管理模块 → 数据库模块
3. **预测执行流程**：用户界面模块 → 模型管理模块 → 数据处理模块
4. **结果展示流程**：各功能模块 → 用户界面模块

### 模块依赖关系
- 用户界面模块作为控制中心，调用其他所有模块
- 训练管理模块依赖模型管理模块和数据处理模块
- 模型管理模块依赖数据处理模块提供的数据格式
- 数据库管理模块相对独立，为其他模块提供存储服务

**【图4-10：模块交互序列图】**

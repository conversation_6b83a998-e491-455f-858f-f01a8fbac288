"""
三层架构演示代码
用户界面层 ◄──► 业务逻辑层 ◄──► 数据存储层
(PyQt6 GUI)    (核心算法)     (SQLite)
"""

import sys
import sqlite3
import json
from datetime import datetime
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout,
                            QHBoxLayout, QWidget, QPushButton, QLabel,
                            QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                            QProgressBar, QMessageBox, QGroupBox)
from PyQt6.QtCore import QThread, pyqtSignal, Qt
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split

# ================================
# 数据存储层 (Data Access Layer)
# ================================

class DataAccessLayer:
    """数据存储层 - 负责所有数据库操作"""

    def __init__(self, db_path="demo.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """初始化数据库表结构"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建模型表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS models (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_type TEXT NOT NULL,
                config TEXT NOT NULL,
                created_time TEXT NOT NULL,
                performance REAL
            )
        ''')

        # 创建训练日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS training_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_id INTEGER,
                epoch INTEGER,
                train_loss REAL,
                val_loss REAL,
                FOREIGN KEY (model_id) REFERENCES models (id)
            )
        ''')

        conn.commit()
        conn.close()

    def save_model(self, model_type, config, performance=None):
        """保存模型信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO models (model_type, config, created_time, performance)
            VALUES (?, ?, ?, ?)
        ''', (model_type, json.dumps(config), datetime.now().isoformat(), performance))

        model_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return model_id

    def save_training_log(self, model_id, epoch, train_loss, val_loss):
        """保存训练日志"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO training_logs (model_id, epoch, train_loss, val_loss)
            VALUES (?, ?, ?, ?)
        ''', (model_id, epoch, train_loss, val_loss))

        conn.commit()
        conn.close()

    def get_all_models(self):
        """获取所有模型"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM models ORDER BY created_time DESC')
        models = cursor.fetchall()

        conn.close()
        return models

    def get_training_logs(self, model_id):
        """获取指定模型的训练日志"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT epoch, train_loss, val_loss
            FROM training_logs
            WHERE model_id = ?
            ORDER BY epoch
        ''', (model_id,))

        logs = cursor.fetchall()
        conn.close()
        return logs

# ================================
# 业务逻辑层 (Business Logic Layer)
# ================================

class SimpleModel(nn.Module):
    """简单的神经网络模型"""
    def __init__(self, input_size, hidden_size, output_size):
        super().__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, output_size)
        self.relu = nn.ReLU()

    def forward(self, x):
        x = self.relu(self.fc1(x))
        x = self.relu(self.fc2(x))
        x = self.fc3(x)
        return x

class BusinessLogicLayer:
    """业务逻辑层 - 负责核心算法和业务处理"""

    def __init__(self, data_layer):
        self.data_layer = data_layer
        self.model = None
        self.scaler = MinMaxScaler()

    def prepare_data(self, data_size=1000):
        """准备示例数据"""
        # 生成示例时序数据
        np.random.seed(42)
        t = np.linspace(0, 4*np.pi, data_size)
        data = np.sin(t) + 0.1 * np.random.randn(data_size)

        # 创建序列数据
        sequence_length = 10
        X, y = [], []
        for i in range(len(data) - sequence_length):
            X.append(data[i:i+sequence_length])
            y.append(data[i+sequence_length])

        X = np.array(X)
        y = np.array(y).reshape(-1, 1)

        # 数据标准化
        X_scaled = self.scaler.fit_transform(X)
        y_scaled = self.scaler.fit_transform(y)

        # 划分训练集和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X_scaled, y_scaled, test_size=0.2, random_state=42
        )

        return (torch.FloatTensor(X_train), torch.FloatTensor(y_train),
                torch.FloatTensor(X_val), torch.FloatTensor(y_val))

    def create_model(self, model_config):
        """创建模型"""
        input_size = model_config['input_size']
        hidden_size = model_config['hidden_size']
        output_size = model_config['output_size']

        self.model = SimpleModel(input_size, hidden_size, output_size)
        return self.model

    def train_model(self, model_config, training_config, progress_callback=None):
        """训练模型"""
        # 准备数据
        X_train, y_train, X_val, y_val = self.prepare_data()

        # 创建模型
        model = self.create_model(model_config)

        # 保存模型到数据库
        model_id = self.data_layer.save_model(
            model_type="SimpleNN",
            config={**model_config, **training_config}
        )

        # 训练配置
        criterion = nn.MSELoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=training_config['learning_rate'])
        epochs = training_config['epochs']

        # 训练循环
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            train_pred = model(X_train)
            train_loss = criterion(train_pred, y_train)

            optimizer.zero_grad()
            train_loss.backward()
            optimizer.step()

            # 验证阶段
            model.eval()
            with torch.no_grad():
                val_pred = model(X_val)
                val_loss = criterion(val_pred, y_val)

            # 保存训练日志
            self.data_layer.save_training_log(
                model_id, epoch, train_loss.item(), val_loss.item()
            )

            # 更新进度
            if progress_callback:
                progress_callback(epoch, epochs, train_loss.item(), val_loss.item())

        return model_id, train_loss.item(), val_loss.item()

    def get_model_list(self):
        """获取模型列表"""
        return self.data_layer.get_all_models()

    def get_model_training_history(self, model_id):
        """获取模型训练历史"""
        return self.data_layer.get_training_logs(model_id)

# ================================
# 用户界面层 (Presentation Layer)
# ================================

class TrainingThread(QThread):
    """训练线程"""
    progress_updated = pyqtSignal(int, int, float, float)  # epoch, total_epochs, train_loss, val_loss
    training_finished = pyqtSignal(int, float, float)  # model_id, final_train_loss, final_val_loss

    def __init__(self, business_layer, model_config, training_config):
        super().__init__()
        self.business_layer = business_layer
        self.model_config = model_config
        self.training_config = training_config

    def run(self):
        """执行训练"""
        def progress_callback(epoch, total_epochs, train_loss, val_loss):
            self.progress_updated.emit(epoch, total_epochs, train_loss, val_loss)

        model_id, final_train_loss, final_val_loss = self.business_layer.train_model(
            self.model_config, self.training_config, progress_callback
        )

        self.training_finished.emit(model_id, final_train_loss, final_val_loss)

class PresentationLayer(QMainWindow):
    """用户界面层 - 负责用户交互"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("三层架构演示 - 时序数据分析系统")
        self.setGeometry(100, 100, 800, 600)

        # 初始化业务逻辑层和数据存储层
        self.data_layer = DataAccessLayer()
        self.business_layer = BusinessLogicLayer(self.data_layer)

        # 训练线程
        self.training_thread = None

        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # 模型配置区域
        model_group = QGroupBox("模型配置")
        model_layout = QVBoxLayout(model_group)

        # 输入维度
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("输入维度:"))
        self.input_size_spin = QSpinBox()
        self.input_size_spin.setRange(1, 100)
        self.input_size_spin.setValue(10)
        input_layout.addWidget(self.input_size_spin)
        model_layout.addLayout(input_layout)

        # 隐藏层维度
        hidden_layout = QHBoxLayout()
        hidden_layout.addWidget(QLabel("隐藏层维度:"))
        self.hidden_size_spin = QSpinBox()
        self.hidden_size_spin.setRange(1, 500)
        self.hidden_size_spin.setValue(64)
        hidden_layout.addWidget(self.hidden_size_spin)
        model_layout.addLayout(hidden_layout)

        # 输出维度
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出维度:"))
        self.output_size_spin = QSpinBox()
        self.output_size_spin.setRange(1, 10)
        self.output_size_spin.setValue(1)
        output_layout.addWidget(self.output_size_spin)
        model_layout.addLayout(output_layout)

        layout.addWidget(model_group)

        # 训练配置区域
        training_group = QGroupBox("训练配置")
        training_layout = QVBoxLayout(training_group)

        # 学习率
        lr_layout = QHBoxLayout()
        lr_layout.addWidget(QLabel("学习率:"))
        self.learning_rate_spin = QDoubleSpinBox()
        self.learning_rate_spin.setRange(0.0001, 1.0)
        self.learning_rate_spin.setValue(0.001)
        self.learning_rate_spin.setDecimals(4)
        lr_layout.addWidget(self.learning_rate_spin)
        training_layout.addLayout(lr_layout)

        # 训练轮数
        epochs_layout = QHBoxLayout()
        epochs_layout.addWidget(QLabel("训练轮数:"))
        self.epochs_spin = QSpinBox()
        self.epochs_spin.setRange(1, 1000)
        self.epochs_spin.setValue(100)
        epochs_layout.addWidget(self.epochs_spin)
        training_layout.addLayout(epochs_layout)

        layout.addWidget(training_group)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.train_button = QPushButton("开始训练")
        self.train_button.clicked.connect(self.start_training)
        button_layout.addWidget(self.train_button)

        self.refresh_button = QPushButton("刷新模型列表")
        self.refresh_button.clicked.connect(self.refresh_model_list)
        button_layout.addWidget(self.refresh_button)

        layout.addLayout(button_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)

        # 状态显示
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)

        # 日志显示
        log_group = QGroupBox("训练日志")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        log_layout.addWidget(self.log_text)

        layout.addWidget(log_group)

        # 模型列表
        model_list_group = QGroupBox("已保存的模型")
        model_list_layout = QVBoxLayout(model_list_group)

        self.model_list_text = QTextEdit()
        self.model_list_text.setMaximumHeight(150)
        model_list_layout.addWidget(self.model_list_text)

        layout.addWidget(model_list_group)

        # 初始化模型列表
        self.refresh_model_list()

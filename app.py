import json
import sys
from typing import Union, Optional
import inspect
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import logging
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QFileDialog, QLabel,
                             QComboBox, QSpinBox, QTabWidget, QMessageBox,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QGroupBox, QFormLayout, QDoubleSpinBox, QLineEdit,
                             QDialog, QSplitter, QTextEdit, QProgressBar,
                             QVBoxLayout as QDialogVBoxLayout, # Alias to avoid conflicts
                             QTableWidget as QDialogTableWidget,
                             QTableWidgetItem as QDialogTableWidgetItem,
                             QHeaderView as QDialogHeaderView,
                             QPushButton as QDialogButton,
                             QAbstractItemView, QStatusBar, QCheckBox,
                             QSizePolicy, QScrollArea) # Added more widgets
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QTimer, QSettings # Added QTimer, QSettings
from PyQt6.QtGui import QTextCursor, QPalette, QColor, QAction # Added QTextCursor, Palette, QColor, QAction
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.dates as mdates # For formatting dates on plots
from torch.utils.data import DataLoader, TensorDataset
# Import the extended TimeSeriesModel and metrics
from models import TimeSeriesModel, calculate_mae, calculate_rmse, calculate_r2
# 导入新的通用时序数据处理函数
from data import (load_time_series_data, preprocess_time_series, create_time_series_features,
                 normalize_time_series, DATA_TYPES, COLUMN_MAPPINGS, create_custom_feature_config,
                 # 保留旧函数以兼容现有代码
                 load_weather_data, preprocess_weather, create_time_features, normalize_weather_data)
from DB import DatabaseManager
from datetime import datetime, timedelta
import time
import traceback
import matplotlib.pyplot as plt
import math
import os
import configparser # 用于读取配置文件


# 应用程序配置
CONFIG_FILE = 'app_settings.ini'

# 为了兼容性，保留原始的COLUMN_MAPPING
COLUMN_MAPPING = COLUMN_MAPPINGS['WEATHER']

# 当前数据类型
CURRENT_DATA_TYPE = 'WEATHER'

def load_app_config():
    """从INI文件加载应用程序配置。"""
    config = configparser.ConfigParser()
    defaults = {
        'Paths': {'database_name': 'model_results.db', 'last_data_dir': '.'},
        'Defaults': {'target_column': 'temperature', 'seq_length': '24', 'epochs': '50', 'learning_rate': '0.001'},
        'UI': {'theme': 'default'} # 示例：添加主题设置
    }
    # 如果配置文件不存在，则创建默认配置
    if not os.path.exists(CONFIG_FILE):
        for section, options in defaults.items():
            config[section] = options
        try:
            with open(CONFIG_FILE, 'w') as configfile:
                config.write(configfile)
            print(f"已创建默认配置文件: {CONFIG_FILE}")
        except IOError as e:
            print(f"警告: 无法写入默认配置文件: {e}")
            # 如果写入失败，使用硬编码的默认值
            config = configparser.ConfigParser() # 重置
            for section, options in defaults.items():
                config[section] = options
    else:
        try:
            config.read(CONFIG_FILE)
            # 确保所有部分/选项存在，如果缺少则添加
            for section, options in defaults.items():
                if not config.has_section(section):
                    config.add_section(section)
                for key, value in options.items():
                    if not config.has_option(section, key):
                        config.set(section, key, value)
            print(f"已从以下位置加载配置: {CONFIG_FILE}")
        except configparser.Error as e:
            print(f"读取配置文件 {CONFIG_FILE} 时出错: {e}。使用默认值。")
            # 读取错误时回退到默认值
            config = configparser.ConfigParser()
            for section, options in defaults.items():
                config[section] = options

    return config

def save_app_config(config):
    """将应用程序配置保存到INI文件。"""
    try:
        with open(CONFIG_FILE, 'w') as configfile:
            config.write(configfile)
        print(f"配置已保存到: {CONFIG_FILE}")
    except IOError as e:
        print(f"保存配置文件时出错: {e}")


# 日志设置
# 创建用于QTextEdit的日志处理器
class QTextEditLogger(logging.Handler):
    def __init__(self, text_edit_widget):
        super().__init__()
        self.widget = text_edit_widget
        self.widget.setReadOnly(True)
        logger.setLevel(logging.DEBUG)

    def emit(self, record):
        msg = self.format(record)
        # 根据日志级别添加适当颜色的消息
        log_level = record.levelno
        if log_level >= logging.ERROR:
            color = "red"
        elif log_level >= logging.WARNING:
            color = "orange"
        elif log_level >= logging.INFO:
            color = "black"
        else: # DEBUG
            color = "gray"

        # 使用HTML进行着色
        colored_msg = f'<font color="{color}">{msg}</font>'
        self.widget.append(colored_msg)
        # 自动滚动到底部
        self.widget.moveCursor(QTextCursor.MoveOperation.End)

# 基本日志配置
log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%H:%M:%S')
logger = logging.getLogger(__name__) # 获取根日志记录器或特定日志记录器
logger.setLevel(logging.DEBUG) # 设置默认级别
# 如果有默认处理程序，则移除以避免控制台输出重复
# for handler in logger.handlers[:]: logger.removeHandler(handler)
# 控制台处理程序（可选，如果你仍然想要控制台输出）
# console_handler = logging.StreamHandler(sys.stdout)
# console_handler.setFormatter(log_formatter)
# logger.addHandler(console_handler)


# Matplotlib样式配置
def setup_matplotlib_style():
    """设置matplotlib绘图样式。"""
    try:
        plt.style.use('seaborn-v0_8-darkgrid') # 示例样式
        plt.rcParams['font.family'] = 'SimHei' # 或其他支持中文的字体
        plt.rcParams['axes.unicode_minus'] = False # 正确显示负号
        plt.rcParams['figure.figsize'] = (10, 6) # 默认图形大小
        plt.rcParams['figure.autolayout'] = True # 自动调整布局
        plt.rcParams['lines.linewidth'] = 1.5
        plt.rcParams['lines.markersize'] = 4
        plt.rcParams['axes.labelsize'] = 10
        plt.rcParams['axes.titlesize'] = 12
        plt.rcParams['xtick.labelsize'] = 8
        plt.rcParams['ytick.labelsize'] = 8
        plt.rcParams['legend.fontsize'] = 9
        print("Matplotlib样式已配置。")
    except Exception as e:
        print(f"警告: 无法设置matplotlib样式: {e}")
        # 如果样式设置失败，使用备用设置
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['axes.unicode_minus'] = False

setup_matplotlib_style()


# 训练线程
class TrainingThread(QThread):
    """后台执行模型训练的线程"""
    # 信号：轮次、训练损失、验证损失、当前学习率
    update_progress = pyqtSignal(int, float, float, float)
    # 信号：日志历史、最终验证损失、运行轮次、最小验证损失、最小损失轮次、最终训练损失、总时间（秒）、参数数量、使用的设备
    training_finished = pyqtSignal(list, float, int, float, int, float, float, int, str)
    training_error = pyqtSignal(str)
    status_update = pyqtSignal(str) # 用于一般状态消息

    def __init__(self, model: TimeSeriesModel, # Type hint
                 train_loader, val_loader, epochs, lr=0.001, early_stopping_patience=10,
                 early_stopping_min_delta=0.00001, # 添加最小改善阈值参数
                 device='cpu', criterion=None, callbacks=None, parent=None):
        super().__init__(parent)
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.epochs_requested = epochs
        self.initial_lr = lr
        self.early_stopping_patience = early_stopping_patience
        self.early_stopping_min_delta = early_stopping_min_delta  # 保存最小改善阈值
        self.device = device
        self.criterion = criterion or nn.MSELoss() # Use default if none provided
        self.callbacks = callbacks or []
        self._is_running = True
        self.log_history = [] # Store (epoch, train_loss, val_loss)
        self.final_val_loss_achieved = float('inf') # Loss in the very last epoch run
        self.epochs_run = 0
        self.min_val_loss_achieved = float('inf') # Best loss during training
        self.epoch_at_min_val_loss = 0
        self.final_train_loss_value = float('nan')
        self.best_model_state = None  # 添加保存最佳模型状态的变量
        logger.info("TrainingThread initialized.")

    def run(self):
        """在线程中执行的主训练循环。"""
        logger.info("训练线程开始运行。")
        self.status_update.emit("训练准备中...")
        start_time = time.time()
        device_used_str = "unknown"
        num_trainable_params = 0

        try:
            # 设置设备
            actual_device = torch.device(self.device)
            device_used_str = str(actual_device)
            logger.info(f"   训练设备设置为: {device_used_str}")
            self.status_update.emit(f"设备: {device_used_str}")
            self.model.to(actual_device)
            logger.info("   模型已移至设备。")

            # 获取模型参数
            param_counts = self.model.get_parameter_count()
            num_trainable_params = param_counts['trainable']
            logger.info(f"   可训练参数: {num_trainable_params:,}")

            # 设置优化器和调度器
            # 如果可用，使用模型的设置方法，否则在此处创建
            if not self.model.optimizer or not self.model.scheduler:
                # 如果未在外部完成，则回退或重新设置
                scheduler_patience = max(1, self.early_stopping_patience // 2) if self.early_stopping_patience else 5
                self.model.setup_training(lr=self.initial_lr, scheduler_type='ReduceLROnPlateau',
                                          scheduler_params={'patience': scheduler_patience, 'verbose': False})
                logger.info("   优化器和调度器已在线程内配置。")
            else:
                logger.info("   优化器和调度器已在模型上配置。")


            # 设置回调
            # 为回调提供对模型和训练参数的访问
            callback_params = {'epochs': self.epochs_requested, 'lr': self.initial_lr,
                               'device': actual_device, 'patience': self.early_stopping_patience}
            for cb in self.callbacks:
                cb.set_model(self.model)
                cb.set_params(callback_params)

            # 训练状态初始化
            best_val_loss_early_stop = float('inf')  # 保存最佳验证损失（用于保存最佳模型）
            prev_val_loss = float('inf')  # 保存上一轮的验证损失（用于早停判断）
            epochs_no_improve = 0  # 早停计数器初始化为0
            logger.info(f"   早停机制已初始化: 耐心值={self.early_stopping_patience}, 最小改善阈值={self.early_stopping_min_delta}")
            logger.info(f"   早停计数器已初始化为 {epochs_no_improve}")
            self.log_history = []

            # 回调: 训练开始
            self.status_update.emit("训练开始...")
            for cb in self.callbacks: cb.on_train_begin(logs={'start_time': time.time()})

            # 轮次循环
            for epoch in range(self.epochs_requested):
                if not self._is_running:
                    logger.warning(f"   训练在轮次 {epoch + 1} 被用户中断。")
                    self.status_update.emit("训练已停止 (用户请求)")
                    self.epochs_run = epoch # 记录停止前完成的轮次
                    # 尝试获取最后的损失值（如果可用）
                    if 'epoch_train_loss' in locals(): self.final_train_loss_value = epoch_train_loss
                    if 'epoch_val_loss' in locals(): self.final_val_loss_achieved = epoch_val_loss
                    break # 退出循环

                epoch_logs = {}
                # 回调: 轮次开始
                for cb in self.callbacks: cb.on_epoch_begin(epoch, logs=epoch_logs)

                # 训练阶段
                self.model.train()
                running_train_loss = 0.0
                train_batch_count = 0
                self.status_update.emit(f"轮次 {epoch + 1}/{self.epochs_requested} (训练中...)")

                for i, (inputs, targets) in enumerate(self.train_loader):
                    if not self._is_running: break # 在批次循环中也检查是否运行
                    batch_logs = {}
                    inputs, targets = inputs.to(actual_device), targets.to(actual_device)

                    # 回调: 批次开始
                    for cb in self.callbacks: cb.on_batch_begin(i, logs=batch_logs)

                    self.model.optimizer.zero_grad()
                    outputs = self.model(inputs)

                    if outputs.ndim > 1 and outputs.shape[1] == 1: outputs = outputs.squeeze(1)
                    if targets.ndim > 1 and targets.shape[1] == 1: targets = targets.squeeze(1)

                    try:
                        loss = self.criterion(outputs, targets)
                    except RuntimeError as loss_err:
                        logger.error(f"损失计算期间发生RuntimeError (轮次 {epoch+1}, 批次 {i+1}): {loss_err}")
                        self.status_update.emit(f"错误: 损失计算失败 (轮次 {epoch+1})")
                        raise loss_err # 损失错误时停止训练

                    loss.backward()
                    # 可选: 梯度裁剪（已在提供的代码中）
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                    self.model.optimizer.step()

                    batch_loss = loss.item()
                    running_train_loss += batch_loss * inputs.size(0)
                    train_batch_count += inputs.size(0)

                    batch_logs['loss'] = batch_loss
                    # 回调: 批次结束
                    for cb in self.callbacks: cb.on_batch_end(i, logs=batch_logs)

                if not self._is_running: break # 在训练批次循环后检查

                epoch_train_loss = running_train_loss / train_batch_count if train_batch_count > 0 else 0.0
                self.final_train_loss_value = epoch_train_loss # 更新最后的训练损失
                epoch_logs['train_loss'] = epoch_train_loss

                # 验证阶段
                self.model.eval()
                running_val_loss = 0.0
                val_batch_count = 0
                self.status_update.emit(f"轮次 {epoch + 1}/{self.epochs_requested} (验证中...)")
                with torch.no_grad():
                    for inputs_val, targets_val in self.val_loader:
                        if not self._is_running: break # 在验证循环中检查
                        inputs_val, targets_val = inputs_val.to(actual_device), targets_val.to(actual_device)
                        outputs_val = self.model(inputs_val)

                        if outputs_val.ndim > 1 and outputs_val.shape[1] == 1: outputs_val = outputs_val.squeeze(1)
                        if targets_val.ndim > 1 and targets_val.shape[1] == 1: targets_val = targets_val.squeeze(1)

                        try:
                            loss_val = self.criterion(outputs_val, targets_val)
                            running_val_loss += loss_val.item() * inputs_val.size(0)
                            val_batch_count += inputs_val.size(0)
                        except RuntimeError as loss_err:
                             logger.error(f"验证损失计算期间发生RuntimeError (轮次 {epoch+1}): {loss_err}")
                             self.status_update.emit(f"错误: 验证损失计算失败 (轮次 {epoch+1})")
                             # 跳过批次还是抛出异常？目前，记录并在可能的情况下继续验证
                             continue

                if not self._is_running: break # 在验证循环后检查

                epoch_val_loss = running_val_loss / val_batch_count if val_batch_count > 0 else 0.0
                self.final_val_loss_achieved = epoch_val_loss # 更新最后的验证损失
                epoch_logs['val_loss'] = epoch_val_loss

                # 更新指标和历史记录
                if epoch_val_loss < self.min_val_loss_achieved:
                    self.min_val_loss_achieved = epoch_val_loss
                    self.epoch_at_min_val_loss = epoch + 1
                    # 保存最佳模型状态
                    self.best_model_state = {k: v.cpu().clone() for k, v in self.model.state_dict().items()}
                    logger.info(f"   在轮次 {epoch + 1} 保存最佳模型状态，验证损失: {epoch_val_loss:.6f}")
                    self.status_update.emit(f"保存最佳模型 (轮次 {epoch + 1}, 验证损失: {epoch_val_loss:.6f})")
                self.log_history.append((epoch + 1, epoch_train_loss, epoch_val_loss))

                # 发送进度信号
                current_lr = self.model.optimizer.param_groups[0]['lr']
                self.update_progress.emit(epoch + 1, epoch_train_loss, epoch_val_loss, current_lr)

                # 回调: 轮次结束
                # 必须在调度器步骤和早停检查之前发生
                for cb in self.callbacks: cb.on_epoch_end(epoch, logs=epoch_logs)

                # 调度器步骤
                if self.model.scheduler:
                    if isinstance(self.model.scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                        self.model.scheduler.step(epoch_val_loss)
                    else:
                        self.model.scheduler.step()

                # 早停检查（与上一轮比较）
                if self.early_stopping_patience and self.early_stopping_patience > 0:
                    # 保存最佳模型
                    if epoch_val_loss < best_val_loss_early_stop:
                        best_val_loss_early_stop = epoch_val_loss
                        # 保存最佳模型状态
                        self.best_model_state = {k: v.cpu().clone() for k, v in self.model.state_dict().items()}
                        self.min_val_loss_achieved = epoch_val_loss
                        self.epoch_at_min_val_loss = epoch + 1
                        logger.info(f"   在轮次 {epoch + 1} 保存最佳模型状态，验证损失: {epoch_val_loss:.6f}")
                        self.status_update.emit(f"保存最佳模型 (轮次 {epoch + 1}, 验证损失: {epoch_val_loss:.6f})")

                    # 早停判断（与上一轮比较）
                    if epoch > 0:  # 从第二轮开始判断
                        # 使用最小改善阈值判断是否有显著改善（与上一轮比较）
                        if epoch_val_loss < prev_val_loss - self.early_stopping_min_delta:
                            # 有显著改善，减少计数器
                            improvement = prev_val_loss - epoch_val_loss

                            # 记录改善前的计数器值
                            old_counter = epochs_no_improve

                            # 减少计数器，但不低于0（折中机制）
                            epochs_no_improve = max(0, epochs_no_improve - 1)  # 减1而不是重置为0

                            logger.info(f"   验证损失从 {prev_val_loss:.6f} 改善到 {epoch_val_loss:.6f} (差值: {improvement:.6f})")
                            logger.info(f"   早停计数器: {old_counter} -> {epochs_no_improve} (减1)")
                        else:
                            # 没有显著改善，增加计数器
                            epochs_no_improve += 1
                            logger.info(f"   验证损失相比上一轮 ({prev_val_loss:.6f}) 没有改善。计数器: {epochs_no_improve-1} -> {epochs_no_improve} (加1)")
                            if epochs_no_improve >= self.early_stopping_patience:
                                logger.info(f"   在轮次 {epoch + 1} 触发早停。")
                                self.status_update.emit(f"训练提前停止 (验证损失计数达到 {self.early_stopping_patience})")
                                self.epochs_run = epoch + 1
                                break # 退出轮次循环

                    # 更新上一轮验证损失
                    prev_val_loss = epoch_val_loss
            else: # 循环正常结束（没有break）
                self.epochs_run = self.epochs_requested

            # 训练成功完成（或提前停止）
            end_time = time.time()
            total_training_time_sec = end_time - start_time
            logger.info(f"训练循环完成。运行了 {self.epochs_run} 轮。")

            # 恢复最佳模型状态（如果有）
            if self.best_model_state is not None and self.min_val_loss_achieved < self.final_val_loss_achieved:
                logger.info(f"从轮次 {self.epoch_at_min_val_loss} 恢复最佳模型，验证损失: {self.min_val_loss_achieved:.6f}")
                self.model.load_state_dict(self.best_model_state)
                self.status_update.emit(f"已恢复最佳模型 (轮次 {self.epoch_at_min_val_loss}, 验证损失: {self.min_val_loss_achieved:.6f})")
            else:
                logger.info("保留最终模型（最佳模型就是最终模型或没有保存最佳模型）")
                self.status_update.emit("训练完成 (保留最终模型)")

            # 无论是正常完成还是用户停止，都发送训练完成信号
            train_end_logs = {}

            if self._is_running:
                # 正常完成或早停
                train_end_logs = {'status': 'completed' if self.epochs_run == self.epochs_requested else 'early_stopped',
                                 'best_model_restored': self.best_model_state is not None}
            else:
                # 用户手动停止
                train_end_logs = {'status': 'stopped_by_user',
                                 'best_model_restored': self.best_model_state is not None}
                logger.info("训练被用户手动停止。")

            # 恢复最佳模型（如果有）
            if not self._is_running and self.best_model_state is not None:
                logger.info(f"用户停止训练：从轮次 {self.epoch_at_min_val_loss} 恢复最佳模型，验证损失: {self.min_val_loss_achieved:.6f}")
                self.model.load_state_dict(self.best_model_state)
                self.status_update.emit(f"已恢复最佳模型 (轮次 {self.epoch_at_min_val_loss}, 验证损失: {self.min_val_loss_achieved:.6f})")

            # 发送最终结果（无论是正常完成还是用户停止）
            self.training_finished.emit(
                self.log_history,
                self.final_val_loss_achieved, # 使用最后一轮的损失
                self.epochs_run,
                self.min_val_loss_achieved, # 使用找到的最佳损失
                self.epoch_at_min_val_loss,
                self.final_train_loss_value, # 使用最后一轮的训练损失
                total_training_time_sec,
                num_trainable_params,
                device_used_str
            )


        except Exception as e:
            # 处理训练错误
            error_message = f"训练线程错误: {str(e)}"
            logger.error(error_message, exc_info=True)
            self.status_update.emit(f"训练失败: {error_message[:100]}...") # Truncate long errors
            self.training_error.emit(error_message)
            train_end_logs = {'status': 'error', 'error_message': str(e)}

        finally:
            # --- Callback: on_train_end ---
            # Ensure train_end is called even on error or stop
            if 'train_end_logs' not in locals():  # If error happened before loop end
                train_end_logs = {'status': 'unknown_exit'}  # Should not happen if try/except/finally is correct
            for cb in self.callbacks: cb.on_train_end(logs=train_end_logs)

    def stop(self):
        """请求训练线程停止。"""
        logger.info("收到训练线程停止请求。")
        self.status_update.emit("正在停止训练...")
        self._is_running = False


# 指标比较对话框（略微增强）
class MetricComparisonDialog(QDialog):
    """显示模型指标对比表格的对话框（增强版）"""
    def __init__(self, comparison_data, parent=None):
        super().__init__(parent)
        self.setWindowTitle("模型指标对比")
        self.setMinimumSize(1000, 500) # 增加尺寸

        layout = QDialogVBoxLayout(self)
        self.table = QDialogTableWidget()
        self.table.setSortingEnabled(True) # 允许按列排序
        layout.addWidget(self.table)

        # 添加描述标签
        layout.addWidget(QLabel("点击表头可按指标排序。将鼠标悬停在配置摘要上可查看完整配置。"))

        button_layout = QHBoxLayout() # 按钮的布局
        # 可选：添加导出按钮
        # self.export_button = QDialogButton("导出为 CSV")
        # self.export_button.clicked.connect(self.export_to_csv)
        # button_layout.addWidget(self.export_button)
        button_layout.addStretch() # 将关闭按钮推到右侧
        self.close_button = QDialogButton("关闭")
        self.close_button.clicked.connect(self.accept)
        button_layout.addWidget(self.close_button)
        layout.addLayout(button_layout)

        self.comparison_data = comparison_data # Store data for potential export
        self.populate_table(comparison_data)

    def format_value(self, value, key):
        """根据键类型格式化表格值的辅助函数。"""
        if value is None or value == float('nan'):
            return "N/A"
        try:
            if key.endswith('_loss'):
                return f"{float(value):.6f}"
            elif key == 'total_training_time_sec':
                return f"{float(value):.2f}"
            elif key == 'num_trainable_params':
                 return f"{int(value):,}" # Add thousand separators
            elif key == 'config':
                 # Simplified config display (already handled in main app _get_short_config_display)
                 # Here we assume it's already a dict or string representation
                 if isinstance(value, dict):
                    # Create a more concise summary for the cell if needed again
                    display_cfg = {k: value[k] for k in ['hidden_dim', 'num_layers', 'dropout', 'nhead', 'tcn_kernel_size'] if k in value}
                    if 'tcn_num_channels' in value:
                        channels = value['tcn_num_channels']
                        display_cfg['tcn_channels'] = f"[{len(channels)}] " + str(channels[:2]) + ('...' if len(channels) > 2 else '')
                    return str(display_cfg) if display_cfg else "{}"
                 else:
                     return str(value) # Should be the short string already
            elif isinstance(value, (int, float, np.number)):
                return str(value) # Default for other numbers
            else:
                return str(value) # Default for strings etc.
        except (ValueError, TypeError):
            return str(value) # Fallback

    def populate_table(self, data):
        """填充比较表格并应用格式化。"""
        if not data:
            self.table.setRowCount(0)
            self.table.setColumnCount(0)
            return

        # 定义列和表头
        # 顺序对显示很重要
        headers_map = {
            'id': 'ID',
            'model_type': '类型',
            'min_val_loss': '最低验证损失',
            'epoch_at_min_val_loss': '达最低损失轮次',
            'final_val_loss': '最终验证损失',
            'final_train_loss': '最终训练损失',
            'epochs_run': '运行轮次',
            'total_training_time_sec': '训练时长(秒)',
            'num_trainable_params': '参数量',
            'seq_length': '序列长度',
            'input_dim': '输入维度',
            'device_used': '设备',
            'config': '配置摘要'
        }
        display_keys = list(headers_map.keys())
        header_labels = list(headers_map.values())

        self.table.setColumnCount(len(display_keys))
        self.table.setHorizontalHeaderLabels(header_labels)
        self.table.setRowCount(len(data))

        # 查找最佳值以进行高亮显示（可选）
        best_metrics = {}
        metrics_to_compare = ['min_val_loss', 'final_val_loss', 'total_training_time_sec'] # 越低越好
        for key in metrics_to_compare:
            valid_values = [m.get(key) for m in data if isinstance(m.get(key), (int, float)) and not math.isnan(m.get(key))]
            if valid_values:
                 best_metrics[key] = min(valid_values)

        # 填充表格单元格
        for row_idx, model_info in enumerate(data):
            full_config_str = json.dumps(model_info.get('config', {}), indent=2) # 用于工具提示

            for col_idx, key in enumerate(display_keys):
                value = model_info.get(key)
                display_value = self.format_value(value, key)

                # 使用QTableWidgetItem进行排序功能，适当转换数值
                try:
                    # 尝试将格式化的数字转换回float/int以进行排序
                    if key.endswith('_loss') or key == 'total_training_time_sec':
                        item = QTableWidgetItem()
                        item.setData(Qt.ItemDataRole.EditRole, float(value) if value is not None and value != 'N/A' else -1e12) # 对N/A使用大的负数进行排序？或者处理N/A？
                        item.setText(display_value) # 显示格式化文本
                    elif key in ['id', 'epoch_at_min_val_loss', 'epochs_run', 'seq_length', 'input_dim', 'num_trainable_params']:
                         item = QTableWidgetItem()
                         item.setData(Qt.ItemDataRole.EditRole, int(value) if value is not None and value != 'N/A' else -1)
                         item.setText(display_value)
                    else: # 文本列（类型、设备、配置）
                        item = QTableWidgetItem(display_value)
                except (ValueError, TypeError):
                     item = QTableWidgetItem(display_value) # 错误或非数字文本的后备方案


                # 为配置列设置工具提示
                if key == 'config':
                    item.setToolTip(full_config_str)

                # 高亮显示最佳指标值
                if key in best_metrics and value == best_metrics[key]:
                    font = item.font()
                    font.setBold(True)
                    item.setFont(font)
                    item.setBackground(QColor("lightgreen")) # 高亮背景

                # 设置文本对齐（可选）
                if isinstance(value, (int, float, np.number)):
                    item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                else:
                    item.setTextAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)

                self.table.setItem(row_idx, col_idx, item)

        self.table.resizeColumnsToContents()
        # 只允许配置列拉伸，其他列适应内容
        header = self.table.horizontalHeader()
        for i in range(len(display_keys)):
            if display_keys[i] == 'config':
                header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)
            else:
                header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)

        self.table.setEditTriggers(QDialogTableWidget.EditTrigger.NoEditTriggers)

    # def export_to_csv(self):
    #     """导出比较数据到CSV的占位函数。"""
    #     if not self.comparison_data:
    #         QMessageBox.warning(self, "无法导出", "没有可导出的对比数据。")
    #         return
    #
    #     file_path, _ = QFileDialog.getSaveFileName(self, "保存对比数据", "", "CSV Files (*.csv)")
    #     if not file_path:
    #         return
    #
    #     try:
    #         df_to_export = pd.DataFrame(self.comparison_data)
    #         # 可能需要展平配置字典以获得更好的CSV表示
    #         # df_to_export['config_str'] = df_to_export['config'].apply(lambda x: json.dumps(x))
    #         # df_to_export = df_to_export.drop(columns=['config'])
    #         df_to_export.to_csv(file_path, index=False, encoding='utf-8-sig') # 使用utf-8-sig以兼容Excel
    #         QMessageBox.information(self, "导出成功", f"对比数据已成功导出到:\n{file_path}")
    #     except Exception as e:
    #         QMessageBox.critical(self, "导出失败", f"导出数据时发生错误:\n{str(e)}")
    #         logger.error(f"导出比较数据失败: {e}", exc_info=True)


# 主应用程序窗口（显著扩展）
class WeatherAnalyzerApp(QMainWindow):
    """主应用程序窗口，用于时序数据分析和模型训练。"""

    # 类常量
    APP_NAME = "时序数据分析与预测平台"
    APP_VERSION = "1.2.0" # 增加版本号，支持多种时序数据类型

    def __init__(self):
        """初始化应用程序"""
        super().__init__()
        logger.info(f"--- Initializing {self.APP_NAME} v{self.APP_VERSION} ---")
        self.app_config = load_app_config() # Load config first

        # 核心应用程序属性
        self.data = None                    # 归一化数据（DataFrame）
        self.scaler = None                  # 缩放器对象（MinMaxScaler）
        self.numeric_cols = None            # 已归一化的列列表
        self.numeric_col_indices_map = {}   # 列名到numeric_cols中索引的映射
        self.feature_cols = None            # 用作输入的特征列表（不包括目标）
        self.feature_col_indices_map = {}   # 特征列名到feature_cols中索引的映射
        self.target_col = self.app_config.get('Defaults', 'target_column', fallback='temperature') # 目标列名
        self.target_col_index_in_numeric = None # 目标列在numeric_cols中的索引
        self.model: Optional[TimeSeriesModel] = None # 当前活动的模型实例（类型提示）
        self.current_model_id = None        # 数据库中活动模型的ID
        self.current_model_config = {}      # 活动模型的配置
        self.last_scaled_numeric_row = None # 数值缩放数据的最后一行（用于预测上下文）
        self.featured_df_before_norm = None # 归一化前的DataFrame（用于递归预测）
        self.db_manager = DatabaseManager(self.app_config.get('Paths', 'database_name', fallback='model_results.db'))
        self.training_thread: Optional[TrainingThread] = None # 活动的训练线程
        self.is_training = False            # 指示训练是否活动的标志

        # UI相关属性
        self.status_bar = QStatusBar()
        self.progress_bar = QProgressBar()
        self.log_text_edit = QTextEdit() # 用于在UI中显示日志
        self.plot_colors = plt.get_cmap('tab10').colors # 使用tab10颜色映射

        # Matplotlib图形和画布
        self.data_fig = Figure(figsize=(10, 6)) # 初始保持tight_layout=False以获得更好的控制
        self.data_canvas = FigureCanvas(self.data_fig)
        self.train_fig = Figure(figsize=(10, 4))
        self.train_canvas = FigureCanvas(self.train_fig)
        self.loss_ax = self.train_fig.add_subplot(111)
        self.lr_ax = None # 训练图上学习率的次要轴

        # 默认训练参数（可通过UI/配置覆盖）
        self.seq_length = self.app_config.getint('Defaults', 'seq_length', fallback=24)
        self.epochs = self.app_config.getint('Defaults', 'epochs', fallback=50)
        # 使用更合理的默认学习率
        self.learning_rate = self.app_config.getfloat('Defaults', 'learning_rate', fallback=0.001)
        self.early_stopping_patience = 10

        # UI控制小部件（为清晰起见在此定义）
        # 数据加载
        self.btn_load = QPushButton("加载气象数据 (.csv)")
        self.target_column_selector = QComboBox() # 允许选择目标列

        # 模型设置
        self.model_selector = QComboBox()
        self.seq_length_selector = QSpinBox()
        self.hidden_dim_label = QLabel("隐藏层维度:")
        self.hidden_dim_spin = QSpinBox()
        self.num_layers_label = QLabel("模型层数:")
        self.num_layers_spin = QSpinBox()
        self.lr_label = QLabel("学习率:")
        self.lr_double_spin = QDoubleSpinBox()
        self.dropout_label = QLabel("Dropout 比率:")
        self.dropout_double_spin = QDoubleSpinBox()
        self.nhead_label = QLabel("Transformer 头数:")
        self.nhead_spin = QSpinBox()
        self.tcn_channels_label = QLabel("TCN 通道 (例: 32,64):")
        self.tcn_channels_edit = QLineEdit()
        self.tcn_kernel_label = QLabel("TCN 卷积核大小:")
        self.tcn_kernel_spin = QSpinBox()
        # 存储小部件以便于访问/可见性控制
        self.hp_widgets = {} # 将在init_ui中填充

        # 训练控制
        self.epoch_selector = QSpinBox()
        self.early_stop_spin = QSpinBox()
        self.btn_train = QPushButton("开始训练")
        self.btn_stop_train = QPushButton("停止训练")

        # 预测
        self.btn_predict_single = QPushButton("显示最新单步预测")
        self.btn_predict_24h = QPushButton("多步预测 (递归)")
        self.oracle_mode_checkbox = QCheckBox("启用Oracle调试模式 (递归)")  # 增加复选框定义
        self.btn_evaluate_loaded = QPushButton("评估当前模型") # 用于评估当前模型的按钮

        # 模型比较/管理
        self.model_table = QTableWidget()
        self.btn_refresh_models = QPushButton("刷新模型列表")
        self.btn_view_logs = QPushButton("查看日志 (单个)")
        self.btn_compare_logs = QPushButton("对比日志曲线 (多个)")
        self.btn_compare_metrics = QPushButton("对比摘要指标 (多个)")
        self.btn_load_selected_model = QPushButton("加载选中模型") # 从表格加载模型
        self.btn_delete_selected_model = QPushButton("删除选中模型") # 从数据库删除模型

        # 初始化UI
        self.init_ui()
        self.update_status_bar("应用程序已启动。")
        self.load_model_comparison_data() # 加载初始模型列表

        # 设置日志处理器
        log_handler = QTextEditLogger(self.log_text_edit)
        log_handler.setFormatter(log_formatter)
        logger.addHandler(log_handler) # 将处理器添加到根日志记录器或特定应用日志记录器
        logger.info("应用程序界面和日志记录器初始化完成。")


    def init_ui(self):
        """初始化用户界面布局和控件。"""
        self.setWindowTitle(f"{self.APP_NAME} v{self.APP_VERSION}")
        self.setGeometry(50, 50, 1600, 900) # 增加默认尺寸

        # 主布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        # 使用QSplitter实现可调整大小的面板
        main_splitter = QSplitter(Qt.Orientation.Horizontal, main_widget)
        main_layout = QHBoxLayout(main_widget) # 使用此布局来容纳分割器
        main_layout.addWidget(main_splitter)

        # 左侧控制面板
        control_panel_widget = QWidget()
        control_layout = QVBoxLayout(control_panel_widget)
        control_panel_widget.setMinimumWidth(350) # 设置控制面板的最小宽度
        control_panel_widget.setMaximumWidth(500) # 设置最大宽度

        # 如果控制面板太长，使用QScrollArea
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_content_widget) # 可滚动内容的布局
        scroll_area.setWidget(scroll_content_widget)
        control_layout.addWidget(scroll_area) # 将滚动区域添加到主控制布局

        # 数据加载组
        data_group = QGroupBox("数据处理与设置")
        data_layout = QFormLayout(data_group) # 使用FormLayout进行标签-小部件对
        self.btn_load.setText("加载时序数据")  # 更新按钮文本
        self.btn_load.setToolTip("加载不同类型的时序数据，包括气象、股票、能源等")
        self.btn_load.clicked.connect(self.load_data)
        data_layout.addRow(self.btn_load) # 按钮占据整行

        # 目标列选择（在数据加载后填充）
        self.target_column_selector.setToolTip("选择要预测的目标变量（需要先加载数据）")
        self.target_column_selector.setEnabled(False) # 在数据加载前禁用
        self.target_column_selector.currentTextChanged.connect(self.on_target_column_changed)
        data_layout.addRow("目标列:", self.target_column_selector)

        scroll_layout.addWidget(data_group) # 添加到可滚动布局

        # 模型设置组
        model_group = QGroupBox("模型配置")
        model_layout = QFormLayout(model_group)
        self.model_selector.addItems(['LSTM', 'GRU', 'Transformer', 'TCN'])
        self.model_selector.setToolTip("选择要使用的模型架构")
        self.model_selector.currentIndexChanged.connect(self.update_hyperparameter_ui)
        model_layout.addRow("模型架构:", self.model_selector)

        self.seq_length_selector.setRange(6, 336) # 增加最大序列长度
        self.seq_length_selector.setValue(self.seq_length)
        self.seq_length_selector.setToolTip("模型输入的历史时间步数量")
        self.seq_length_selector.valueChanged.connect(self.update_seq_length)
        model_layout.addRow("序列长度:", self.seq_length_selector)

        # 设置超参数小部件字典
        self.hp_widgets = {
            'hidden_dim': (self.hidden_dim_label, self.hidden_dim_spin),
            'num_layers': (self.num_layers_label, self.num_layers_spin),
            'lr': (self.lr_label, self.lr_double_spin),
            'dropout': (self.dropout_label, self.dropout_double_spin),
            'nhead': (self.nhead_label, self.nhead_spin),
            'tcn_channels': (self.tcn_channels_label, self.tcn_channels_edit),
            'tcn_kernel': (self.tcn_kernel_label, self.tcn_kernel_spin)
        }
        # 设置范围和默认值
        self.hidden_dim_spin.setRange(8, 1024); self.hidden_dim_spin.setValue(64); self.hidden_dim_spin.setSingleStep(8)
        self.num_layers_spin.setRange(1, 8); self.num_layers_spin.setValue(2)
        self.lr_double_spin.setRange(1e-6, 0.1); self.lr_double_spin.setSingleStep(0.0001); self.lr_double_spin.setValue(self.learning_rate); self.lr_double_spin.setDecimals(6)
        self.dropout_double_spin.setRange(0.0, 0.7); self.dropout_double_spin.setSingleStep(0.05); self.dropout_double_spin.setValue(0.1); self.dropout_double_spin.setDecimals(2)
        self.nhead_spin.setRange(1, 16); self.nhead_spin.setValue(4) # 必须是hidden_dim的除数
        self.tcn_channels_edit.setText("32, 64, 128"); self.tcn_channels_edit.setToolTip("逗号分隔的通道数列表")
        self.tcn_kernel_spin.setRange(2, 9); self.tcn_kernel_spin.setValue(5); self.tcn_kernel_spin.setSingleStep(1)
        # 将小部件添加到布局（可见性由update_hyperparameter_ui控制）
        for label, widget in self.hp_widgets.values():
            model_layout.addRow(label, widget)

        scroll_layout.addWidget(model_group)

        # 训练控制组
        train_group = QGroupBox("训练控制")
        train_layout = QFormLayout(train_group)
        self.epoch_selector.setRange(1, 5000); self.epoch_selector.setValue(self.epochs); self.epoch_selector.setSingleStep(10)
        train_layout.addRow("最大轮次:", self.epoch_selector)
        self.early_stop_spin.setRange(3, 100); self.early_stop_spin.setValue(self.early_stopping_patience); self.early_stop_spin.setToolTip("验证损失多少轮不改善则停止 (0禁用)")
        train_layout.addRow("早停耐心:", self.early_stop_spin)

        # 添加最小改善阈值控件
        self.early_stop_min_delta_spin = QDoubleSpinBox()
        self.early_stop_min_delta_spin.setDecimals(5)  # 设置为5位小数，刚好显示0.00001
        self.early_stop_min_delta_spin.setRange(0.0000001, 0.01)  # 设置范围，确保最小值小于默认值
        self.early_stop_min_delta_spin.setSingleStep(0.00001)  # 设置步长
        self.early_stop_min_delta_spin.setValue(0.00001)  # 最后设置值
        self.early_stop_min_delta_spin.setToolTip("验证损失需要改善超过此值才算有效改善")
        train_layout.addRow("最小改善阈值:", self.early_stop_min_delta_spin)

        # 在水平布局中的按钮
        train_button_layout = QHBoxLayout()
        self.btn_train.clicked.connect(self.start_training)
        self.btn_train.setEnabled(False)
        self.btn_train.setToolTip("加载数据后启用")
        train_button_layout.addWidget(self.btn_train)
        self.btn_stop_train.clicked.connect(self.stop_training)
        self.btn_stop_train.setEnabled(False)
        self.btn_stop_train.setToolTip("训练开始后启用")
        train_button_layout.addWidget(self.btn_stop_train)
        train_layout.addRow(train_button_layout) # 将按钮布局添加为一行

        scroll_layout.addWidget(train_group)

        # --- Prediction & Evaluation Group ---
        predict_group = QGroupBox("预测与评估")
        predict_layout = QVBoxLayout(predict_group) # 使用垂直布局

        # 添加当前模型目标列标签
        self.model_target_label = QLabel("当前模型: 未加载")
        self.model_target_label.setStyleSheet("font-weight: bold; color: #555;")
        self.model_target_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        predict_layout.addWidget(self.model_target_label)

        print(f"DEBUG: Connecting btn_predict_single to {self.show_prediction_result}")
        self.btn_predict_single.clicked.connect(self.show_prediction_result)
        self.btn_predict_single.setEnabled(False)
        self.btn_predict_single.setToolTip("模型训练完成或加载后启用")
        predict_layout.addWidget(self.btn_predict_single)

        # 多步预测按钮
        self.btn_predict_24h.clicked.connect(self.call_predict_next_day_recursive)  # 修改：调用一个新的槽函数
        self.btn_predict_24h.setEnabled(False)
        self.btn_predict_24h.setToolTip("进行多步递归预测（股票数据5步，其他数据类型50步）")
        predict_layout.addWidget(self.btn_predict_24h)

        # Oracle 调试模式复选框
        self.oracle_mode_checkbox.setToolTip(
            "调试功能：如果选中，递归预测非目标特征时会尝试使用未来的真实值。\n"
            "需要 `featured_df_before_norm` 包含相应的未来数据。"
        )
        self.oracle_mode_checkbox.setChecked(False)  # 默认不选中
        # 根据模型和数据状态启用/禁用 (与 predict_24h 按钮联动)
        self.oracle_mode_checkbox.setEnabled(False)
        predict_layout.addWidget(self.oracle_mode_checkbox)

        self.btn_evaluate_loaded.clicked.connect(self.evaluate_current_model)
        self.btn_evaluate_loaded.setEnabled(False)
        self.btn_evaluate_loaded.setToolTip("加载数据和模型后启用")
        predict_layout.addWidget(self.btn_evaluate_loaded)

        scroll_layout.addWidget(predict_group)
        scroll_layout.addStretch() # 将内容向上推

        # 将控制面板小部件（包含滚动区域）添加到分割器
        main_splitter.addWidget(control_panel_widget)


        # 右侧面板（选项卡和日志）
        right_panel_widget = QWidget()
        right_layout = QVBoxLayout(right_panel_widget)

        # 用于图表和比较的选项卡小部件
        self.tab_widget = QTabWidget()
        self.tab_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # 选项卡1：数据可视化/预测图
        self.tab_data_viz = QWidget()
        tab1_layout = QVBoxLayout(self.tab_data_viz)
        tab1_layout.addWidget(self.data_canvas)
        self.tab_widget.addTab(self.tab_data_viz, "数据概览/预测图")

        # 选项卡2：训练监控图
        self.tab_train_monitor = QWidget()
        tab2_layout = QVBoxLayout(self.tab_train_monitor)
        tab2_layout.addWidget(self.train_canvas)
        self.tab_widget.addTab(self.tab_train_monitor, "训练过程监控")

        # 选项卡3：模型比较表
        self.tab_model_compare = QWidget()
        tab3_layout = QVBoxLayout(self.tab_model_compare)
        tab3_layout.addWidget(QLabel("已保存的模型训练记录:"))
        self.model_table.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        self.model_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.model_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.model_table.itemSelectionChanged.connect(self.on_model_table_selection_changed)
        self.model_table.setSortingEnabled(True) # 启用排序
        tab3_layout.addWidget(self.model_table)

        # 模型比较/管理的水平布局按钮
        model_mgmt_layout = QHBoxLayout()
        self.btn_refresh_models.clicked.connect(self.load_model_comparison_data)
        self.btn_refresh_models.setToolTip("重新从数据库加载模型列表")
        model_mgmt_layout.addWidget(self.btn_refresh_models)
        self.btn_load_selected_model.clicked.connect(self.load_selected_model_from_table)
        self.btn_load_selected_model.setEnabled(False)
        self.btn_load_selected_model.setToolTip("选择一个模型后启用")
        model_mgmt_layout.addWidget(self.btn_load_selected_model)
        self.btn_delete_selected_model.clicked.connect(self.delete_selected_model_from_table)
        self.btn_delete_selected_model.setEnabled(False)
        self.btn_delete_selected_model.setToolTip("选择一个或多个模型后启用")
        model_mgmt_layout.addWidget(self.btn_delete_selected_model)
        model_mgmt_layout.addStretch()
        tab3_layout.addLayout(model_mgmt_layout)

        # 比较按钮布局
        compare_button_layout = QHBoxLayout()
        self.btn_view_logs.clicked.connect(self.view_selected_model_logs)
        self.btn_view_logs.setEnabled(False)
        compare_button_layout.addWidget(self.btn_view_logs)
        self.btn_compare_logs.clicked.connect(self.compare_selected_model_logs)
        self.btn_compare_logs.setEnabled(False)
        compare_button_layout.addWidget(self.btn_compare_logs)
        self.btn_compare_metrics.clicked.connect(self.compare_selected_model_metrics)
        self.btn_compare_metrics.setEnabled(False)
        compare_button_layout.addWidget(self.btn_compare_metrics)
        tab3_layout.addLayout(compare_button_layout)

        self.tab_widget.addTab(self.tab_model_compare, "模型比较与管理")

        # 选项卡4：评估指标显示（占位符）
        self.tab_evaluation = QWidget()
        self.eval_layout = QVBoxLayout(self.tab_evaluation)
        self.eval_label = QLabel("模型评估结果将显示在此处。")
        self.eval_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.eval_table = QTableWidget() # 用于显示详细指标的表格
        self.eval_table.setColumnCount(2) # 指标名称，值
        self.eval_table.setHorizontalHeaderLabels(["指标名称", "值"])
        self.eval_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.eval_table.verticalHeader().setVisible(False)
        self.eval_layout.addWidget(self.eval_label)
        self.eval_layout.addWidget(self.eval_table)
        self.eval_layout.addStretch()
        self.tab_widget.addTab(self.tab_evaluation, "模型评估")

        right_layout.addWidget(self.tab_widget) # 将选项卡添加到右侧面板

        # 日志区域
        log_group = QGroupBox("程序日志")
        log_layout = QVBoxLayout(log_group)
        self.log_text_edit.setMinimumHeight(100) # 设置日志区域的最小高度
        self.log_text_edit.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum) # 水平扩展，垂直最小
        log_layout.addWidget(self.log_text_edit)
        right_layout.addWidget(log_group, stretch=1) # 日志区域初始占用较少空间

        # 将右侧面板小部件添加到分割器
        main_splitter.addWidget(right_panel_widget)
        # 设置初始分割器大小（根据需要调整）
        main_splitter.setSizes([400, 1200]) # 给右侧面板更多空间

        # 状态栏
        self.setStatusBar(self.status_bar)
        self.status_bar.addPermanentWidget(self.progress_bar) # 将进度条添加到右侧
        self.progress_bar.setRange(0, 100) # 轮次或百分比
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False) # 初始隐藏
        self.progress_bar.setFixedWidth(200)
        self.progress_bar.setTextVisible(True)

        # 菜单栏（示例）
        self.create_menu_bar()

        # 最终UI设置
        self.update_hyperparameter_ui(self.model_selector.currentIndex()) # 基于默认模型的初始UI状态
        self.on_model_table_selection_changed() # 比较选项卡的初始按钮状态
        logger.info("UI initialization complete.")


    def create_menu_bar(self):
        """创建应用程序菜单栏"""
        menu_bar = self.menuBar()

        # 文件菜单
        file_menu = menu_bar.addMenu("&文件")
        load_action = QAction("&加载数据...", self)
        load_action.triggered.connect(self.load_data)
        file_menu.addAction(load_action)
        # save_state_action = QAction("&保存状态...", self) # 占位符
        # save_state_action.triggered.connect(self.save_app_state) # 占位符
        # file_menu.addAction(save_state_action)
        # load_state_action = QAction("&加载状态...", self) # 占位符
        # load_state_action.triggered.connect(self.load_app_state) # 占位符
        # file_menu.addAction(load_state_action)
        file_menu.addSeparator()
        exit_action = QAction("&退出", self)
        exit_action.triggered.connect(self.close) # 连接到窗口关闭
        file_menu.addAction(exit_action)

        # 编辑菜单（占位符）
        # edit_menu = menu_bar.addMenu("&编辑")
        # settings_action = QAction("&设置...", self)
        # settings_action.triggered.connect(self.open_settings_dialog) # 占位符
        # edit_menu.addAction(settings_action)

        # 帮助菜单
        help_menu = menu_bar.addMenu("&帮助")
        about_action = QAction("&关于...", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)


    def show_about_dialog(self):
        """显示关于对话框"""
        QMessageBox.about(self, f"关于 {self.APP_NAME}",
                          f"<h2>{self.APP_NAME}</h2>"
                          f"<p>版本: {self.APP_VERSION}</p>"
                          f"<p>一个用于多种时序数据分析、模型训练和预测的平台。</p>"
                          f"<p>支持的数据类型：</p>"
                          f"<ul>"
                          f"<li>气象数据 (温度、湿度、气压等)</li>"
                          f"<li>股票数据 (开盘价、收盘价、交易量等)</li>"
                          f"<li>能源数据 (功耗、温度、湿度等)</li>"
                          f"<li>自定义数据 (用户定义的时序数据)</li>"
                          f"</ul>"
                          f"<p>使用 PyQt6, PyTorch, Pandas, Matplotlib等构建。</p>"
                          f"<p>(C) 2024 Your Name/Organization</p>") # 替换为实际信息


    def update_status_bar(self, message, timeout=5000):
        """更新状态栏消息"""
        self.status_bar.showMessage(message, timeout)
        logger.debug(f"状态更新: {message}")


    def update_seq_length(self, value):
        """当序列长度选择器值改变时调用"""
        self.seq_length = value
        logger.info(f"输入序列长度更新为: {self.seq_length}")
        self.update_status_bar(f"序列长度设置为: {self.seq_length}")


    def update_hyperparameter_ui(self, index=None):
        """根据选择的模型类型更新超参数控件的可见性和标签"""
        # 如果index为None，使用当前索引
        if index is None:
            index = self.model_selector.currentIndex()
        model_type = self.model_selector.itemText(index)
        logger.info(f"更新模型类型的超参数UI: {model_type}")

        # 默认可见性
        # 首先隐藏所有特定于模型的小部件
        rnn_widgets = ['hidden_dim', 'num_layers']
        transformer_widgets = ['hidden_dim', 'num_layers', 'nhead']
        tcn_widgets = ['tcn_channels', 'tcn_kernel']
        all_specific_widgets = set(rnn_widgets + transformer_widgets + tcn_widgets)

        for key, (label, widget) in self.hp_widgets.items():
            # 默认隐藏特定小部件
            if key in all_specific_widgets:
                label.setVisible(False)
                widget.setVisible(False)
            # 确保通用小部件（如lr、dropout）始终可见
            elif key in ['lr', 'dropout']:
                 label.setVisible(True)
                 widget.setVisible(True)

        # 根据模型类型设置可见性
        widgets_to_show = []
        if model_type in ['LSTM', 'GRU']:
            widgets_to_show = rnn_widgets
            self.hp_widgets['hidden_dim'][0].setText("隐藏层维度:") # 重置文本
            self.hp_widgets['num_layers'][0].setText("层数:") # 重置文本
        elif model_type == 'Transformer':
            widgets_to_show = transformer_widgets
            self.hp_widgets['hidden_dim'][0].setText("隐藏层维度 (d_model):") # 特定文本
            self.hp_widgets['num_layers'][0].setText("Encoder 层数:") # 特定文本
        elif model_type == 'TCN':
            widgets_to_show = tcn_widgets

        # 显示相关小部件
        for key in widgets_to_show:
             if key in self.hp_widgets:
                 self.hp_widgets[key][0].setVisible(True)
                 self.hp_widgets[key][1].setVisible(True)

        # 如果控件频繁出现/消失，可能需要调整布局间距
        # self.layout().activate() # 强制布局更新

    def load_data(self):
        """加载、预处理和特征工程化时序数据"""
        # 创建数据类型选择对话框
        data_type_dialog = QDialog(self)
        data_type_dialog.setWindowTitle("选择数据类型")
        data_type_dialog.setMinimumWidth(400)

        dialog_layout = QVBoxLayout(data_type_dialog)

        # 添加数据类型选择下拉框
        data_type_label = QLabel("请选择数据类型:")
        dialog_layout.addWidget(data_type_label)

        data_type_combo = QComboBox()
        for data_type, description in DATA_TYPES.items():
            data_type_combo.addItem(f"{description} ({data_type})", data_type)
        dialog_layout.addWidget(data_type_combo)

        # 添加自定义映射选项（仅当选择CUSTOM时显示）
        custom_mapping_group = QGroupBox("自定义列映射")
        custom_mapping_group.setVisible(False)
        custom_mapping_layout = QVBoxLayout(custom_mapping_group)

        custom_mapping_label = QLabel("请输入自定义列映射 (格式: 原始列名=标准列名，每行一个映射):")
        custom_mapping_text = QTextEdit()
        custom_mapping_text.setPlaceholderText("例如:\ndate=timestamp\nvalue=temperature\nprice=close_price")
        custom_mapping_layout.addWidget(custom_mapping_label)
        custom_mapping_layout.addWidget(custom_mapping_text)

        dialog_layout.addWidget(custom_mapping_group)

        # 添加时间列选择
        time_col_group = QGroupBox("时间列设置")
        time_col_layout = QFormLayout(time_col_group)

        time_col_edit = QLineEdit()
        time_col_edit.setPlaceholderText("留空将自动检测")
        time_col_layout.addRow("时间列名称:", time_col_edit)

        dialog_layout.addWidget(time_col_group)

        # 添加按钮
        button_box = QHBoxLayout()
        cancel_button = QPushButton("取消")
        ok_button = QPushButton("确定")
        ok_button.setDefault(True)
        button_box.addWidget(cancel_button)
        button_box.addWidget(ok_button)
        dialog_layout.addLayout(button_box)

        # 连接信号
        def on_data_type_changed(index):
            data_type = data_type_combo.currentData()
            custom_mapping_group.setVisible(data_type == 'CUSTOM')

        data_type_combo.currentIndexChanged.connect(on_data_type_changed)
        cancel_button.clicked.connect(data_type_dialog.reject)
        ok_button.clicked.connect(data_type_dialog.accept)

        # 显示对话框
        if data_type_dialog.exec() != QDialog.DialogCode.Accepted:
            self.update_status_bar("数据加载已取消。", 3000)
            return

        # 获取选择的数据类型
        selected_data_type = data_type_combo.currentData()
        time_col = time_col_edit.text().strip() if time_col_edit.text().strip() else None

        # 处理自定义映射
        custom_mapping = None
        if selected_data_type == 'CUSTOM':
            mapping_text = custom_mapping_text.toPlainText().strip()
            if mapping_text:
                try:
                    custom_mapping = {}
                    for line in mapping_text.split('\n'):
                        if '=' in line:
                            key, value = line.split('=', 1)
                            custom_mapping[key.strip()] = value.strip()
                except Exception as e:
                    QMessageBox.warning(self, "映射格式错误", f"自定义映射格式错误: {str(e)}\n将使用空映射。")
                    custom_mapping = {}

        # 更新全局数据类型
        global CURRENT_DATA_TYPE, COLUMN_MAPPING
        CURRENT_DATA_TYPE = selected_data_type
        if selected_data_type == 'CUSTOM' and custom_mapping:
            COLUMN_MAPPING = custom_mapping
        else:
            COLUMN_MAPPING = COLUMN_MAPPINGS.get(selected_data_type, {})

        # 选择数据文件
        last_dir = self.app_config.get('Paths', 'last_data_dir', fallback='.')
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            f"选择{DATA_TYPES.get(selected_data_type, '时序')}数据文件",
            last_dir,
            "数据文件 (*.csv *.xlsx *.xls *.json)"
        )
        if not file_path:
            self.update_status_bar("数据加载已取消。", 3000)
            return

        # 保存目录以便下次使用
        self.app_config.set('Paths', 'last_data_dir', os.path.dirname(file_path))
        save_app_config(self.app_config)  # 保存更新后的配置

        self.update_status_bar(f"正在加载数据: {os.path.basename(file_path)}...")
        QApplication.processEvents()  # 允许UI更新

        # 在加载期间禁用UI
        self.set_controls_enabled(False)

        try:
            logger.info(f"从以下位置加载数据: {file_path}")
            logger.info(f"数据类型: {selected_data_type}")

            # 使用新的通用加载函数
            raw_df = load_time_series_data(
                file_path,
                data_type=selected_data_type,
                custom_mapping=custom_mapping,
                time_col=time_col
            )

            if raw_df is None:
                raise FileNotFoundError(f"加载数据失败或文件未找到: {file_path}")

            self.update_status_bar("数据加载完成，正在预处理...")
            QApplication.processEvents()
            logger.info("正在预处理数据...")

            # 使用新的通用预处理函数
            processed_df = preprocess_time_series(
                raw_df.copy(),  # 使用副本
                data_type=selected_data_type,
                custom_filters=None  # 使用默认过滤条件
            )

            if processed_df is None or processed_df.empty:
                raise ValueError("预处理后数据为空，请检查过滤条件或原始数据。")

            self.update_status_bar("预处理完成，正在进行特征工程...")
            QApplication.processEvents()
            logger.info("正在创建时间特征...")

            # 使用新的通用特征工程函数
            # 为自定义数据集创建特征工程配置
            custom_config = None
            if selected_data_type == 'CUSTOM':
                logger.info("为自定义数据集创建特征工程配置...")

                # 检查是否是天然气价格数据
                is_natural_gas_data = False
                if 'natural_gas' in file_path.lower():
                    is_natural_gas_data = True
                    logger.info("检测到天然气价格数据，将应用特殊的特征工程配置")

                # 创建自定义特征配置
                custom_config = create_custom_feature_config(processed_df.copy(), self.target_col)

                # 为天然气价格数据增强特征配置
                if is_natural_gas_data:
                    # 确保启用目标列导数特征
                    custom_config['derived_features']['target_derivatives']['enabled'] = True
                    custom_config['derived_features']['target_derivatives']['columns'] = [self.target_col]

                    # 增加滞后特征的范围（适合日频数据）
                    custom_config['lag_features']['enabled'] = True
                    custom_config['lag_features']['columns'] = [self.target_col]
                    custom_config['lag_features']['lags'] = [1, 2, 3, 5, 7, 14, 21, 30, 60, 90]  # 1天到3个月

                    # 调整滚动窗口（适合日频数据）
                    custom_config['rolling_features']['windows'] = {
                        '7d': '7d',    # 1周
                        '14d': '14d',  # 2周
                        '30d': '30d',  # 1个月
                        '90d': '90d'   # 3个月
                    }

                    logger.info("已为天然气价格数据增强特征工程配置")

                logger.info(f"自定义特征工程配置已创建: {len(custom_config['rolling_features']['columns'])} 个列, {len(custom_config['derived_features']['interactions']['pairs'])} 个交互特征")

            # 创建特征
            featured_df = create_time_series_features(
                processed_df.copy(),  # 使用副本
                data_type=selected_data_type,
                custom_config=custom_config,  # 使用自定义配置或默认配置
                target_col=self.target_col  # 传递目标列
            )

            # 检查是否成功创建了导数特征
            if featured_df is not None and not featured_df.empty and self.target_col:
                derivative_features = [
                    f"{self.target_col}_change",
                    f"{self.target_col}_acceleration",
                    f"{self.target_col}_pct_change"
                ]

                created_derivatives = [feat for feat in derivative_features if feat in featured_df.columns]
                if created_derivatives:
                    logger.info(f"成功创建了以下导数特征: {created_derivatives}")
                else:
                    logger.warning(f"未能创建目标列 {self.target_col} 的导数特征")

            if featured_df is None or featured_df.empty:
                raise ValueError("特征工程后数据为空，请检查特征生成逻辑。")

            # 1. 存储完整的特征化数据
            self.featured_df_before_norm = featured_df.copy()
            logger.info(f"完整特征化数据 (self.featured_df_before_norm) shape: {self.featured_df_before_norm.shape}")
            if not self.featured_df_before_norm.empty:
                logger.info(
                    f"  其时间范围: {self.featured_df_before_norm.index.min()} to {self.featured_df_before_norm.index.max()}")

            # 2. 确定用于训练/验证的数据部分
            num_oracle_future_steps = 144 + 24  # 预测24小时，预留1天+24条（4小时）buffer

            if len(self.featured_df_before_norm) < self.seq_length + num_oracle_future_steps + 100:
                logger.error("数据总量不足以进行训练并为Oracle模式预留足够的未来数据。")
                QMessageBox.critical(self, "数据不足", "数据总量不足，请加载更多数据或减少Oracle预留。")
                # 清理状态
                self.data = None;
                self.scaler = None;
                self.numeric_cols = None;
                self.numeric_col_indices_map = {}
                self.feature_cols = None;
                self.feature_col_indices_map = {};
                self.target_col_index_in_numeric = None
                self.last_scaled_numeric_row = None
                self.target_column_selector.clear();
                self.target_column_selector.setEnabled(False)
                self.set_controls_enabled(True)
                self.btn_train.setEnabled(False)
                return

            split_idx_for_oracle_future = len(self.featured_df_before_norm) - num_oracle_future_steps
            df_for_norm_and_train = self.featured_df_before_norm.iloc[:split_idx_for_oracle_future].copy()

            logger.info(f"用于归一化和训练/验证的数据 (df_for_norm_and_train) shape: {df_for_norm_and_train.shape}")
            if not df_for_norm_and_train.empty:
                logger.info(f"  其时间范围: {df_for_norm_and_train.index.min()} to {df_for_norm_and_train.index.max()}")

            # 3. 填充目标列选择器（基于完整特征列表）
            available_columns = self.featured_df_before_norm.columns.tolist()
            self.target_column_selector.clear()
            self.target_column_selector.addItems(available_columns)
            if self.target_col in available_columns:
                self.target_column_selector.setCurrentText(self.target_col)
            elif available_columns:
                self.target_col = available_columns[0]
                self.target_column_selector.setCurrentText(self.target_col)
                logger.warning(f"默认目标列未找到，使用'{self.target_col}'代替")
            else:
                raise ValueError("特征工程后没有可用的列！")
            self.target_column_selector.setEnabled(True)
            logger.info(f"目标列设置为: {self.target_col}")

            # 4. 归一化仅在训练/验证数据上执行
            self.update_status_bar("特征工程完成，正在归一化数据...")
            QApplication.processEvents()
            logger.info("对训练/验证数据部分进行归一化...")

            # 使用新的通用归一化函数
            normalized_df_train_val, self.scalers, self.processed_cols = normalize_time_series(
                df_for_norm_and_train.copy(),
                data_type=selected_data_type,
                custom_config=None,  # 使用默认配置
                target_col=self.target_col  # 传递目标列
            )

            if self.scalers is None or self.processed_cols is None or normalized_df_train_val is None or normalized_df_train_val.empty:
                raise ValueError("训练/验证数据的归一化失败或返回空结果。")

            # 5. 更新核心数据属性
            self.data = normalized_df_train_val  # 仅包含训练/验证的归一化数据
            logger.info(f"归一化后的训练/验证数据 (self.data) shape: {self.data.shape}")

            # 提取不同类型的列
            self.minmax_cols = self.scalers.get('minmax_cols', [])
            self.robust_cols = self.scalers.get('robust_cols', [])
            self.periodic_cols = self.scalers.get('periodic_cols', [])
            self.categorical_cols = self.scalers.get('categorical_cols', [])

            # 记录所有数值列（用于兼容性）
            self.numeric_cols = self.minmax_cols + self.robust_cols

            # 确保目标列在 numeric_cols 中
            if self.target_col not in self.numeric_cols:
                logger.warning(f"目标列 '{self.target_col}' 不在归一化的数值列中，将添加到 numeric_cols")
                self.numeric_cols.append(self.target_col)

                # 如果目标列不在 minmax_cols 中，也添加到 minmax_cols
                if self.target_col not in self.minmax_cols:
                    logger.warning(f"目标列 '{self.target_col}' 不在 minmax_cols 中，将添加到 minmax_cols")
                    self.minmax_cols.append(self.target_col)

            logger.info(f"参与MinMax归一化的列 (self.minmax_cols): {self.minmax_cols}")
            logger.info(f"参与Robust归一化的列 (self.robust_cols): {self.robust_cols}")
            logger.info(f"周期性特征列 (self.periodic_cols): {self.periodic_cols}")
            logger.info(f"分类特征列 (self.categorical_cols): {self.categorical_cols}")

            # 更新特征列和相关映射
            self._update_feature_columns()
            logger.info(f"模型输入特征 (self.feature_cols): {self.feature_cols}")
            self.numeric_col_indices_map = {col: i for i, col in enumerate(self.numeric_cols)}

            # 确保目标列索引正确设置
            if self.target_col in self.numeric_cols:
                self.target_col_index_in_numeric = self.numeric_cols.index(self.target_col)
                logger.info(f"目标列 '{self.target_col}' 索引设置为: {self.target_col_index_in_numeric}")
            else:
                logger.error(f"目标列 '{self.target_col}' 不在归一化的数值列中: {self.numeric_cols}")
                self.target_col_index_in_numeric = None

            # 存储最后一行用于预测上下文（基于训练数据）
            if not self.data.empty and self.numeric_cols:
                self.last_scaled_numeric_row = self.data[self.numeric_cols].iloc[-1].values.copy()
            else:
                self.last_scaled_numeric_row = None

            logger.info("数据加载、预处理、特征工程和部分归一化完成。")
            self.show_data_overview()  # 显示训练/验证部分数据
            QMessageBox.information(self, "成功", "数据加载和处理完成！")
            self.update_status_bar("数据加载和处理完成！", 5000)

            # --- 启用相关控件 ---
            self.btn_train.setEnabled(True)
            self.btn_evaluate_loaded.setEnabled(self.model is not None)

        except (FileNotFoundError, ValueError, KeyError, Exception) as e:
            error_msg = f"数据处理失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            QMessageBox.critical(self, "错误", error_msg)
            self.update_status_bar(f"错误: {error_msg[:100]}...", 10000)
            # 错误时重置数据状态
            self.data = None;
            self.scaler = None;
            self.numeric_cols = None;
            self.numeric_col_indices_map = {}
            self.feature_cols = None;
            self.feature_col_indices_map = {};
            self.target_col_index_in_numeric = None
            self.last_scaled_numeric_row = None
            self.target_column_selector.clear();
            self.target_column_selector.setEnabled(False)
            self.btn_train.setEnabled(False)
            self.btn_predict_single.setEnabled(False)
            self.btn_predict_24h.setEnabled(False)
            self.btn_evaluate_loaded.setEnabled(False)
            # 清理图表
            self.data_fig.clf();
            self.data_canvas.draw()

        finally:
            # 重新启用UI
            self.set_controls_enabled(True)


    def on_target_column_changed(self, new_target_col):
        """当用户在下拉框中选择新的目标列时调用"""
        if not new_target_col or self.data is None:
            return # 如果没有数据或空选择，则忽略

        if new_target_col == self.target_col:
            return # 没有变化

        logger.info(f"用户将目标列更改为: {new_target_col}")
        self.target_col = new_target_col
        self.app_config.set('Defaults', 'target_column', new_target_col) # 更新配置
        save_app_config(self.app_config)

        # 重新计算依赖状态
        try:
             # 更新特征列（排除新目标）
            self._update_feature_columns()

             # 确保目标列在 numeric_cols 中
            if self.target_col not in self.numeric_cols:
                logger.warning(f"目标列 '{self.target_col}' 不在归一化的数值列中，将添加到 numeric_cols")
                self.numeric_cols.append(self.target_col)

                # 如果目标列不在 minmax_cols 中，也添加到 minmax_cols
                if hasattr(self, 'minmax_cols') and self.target_col not in self.minmax_cols:
                    logger.warning(f"目标列 '{self.target_col}' 不在 minmax_cols 中，将添加到 minmax_cols")
                    self.minmax_cols.append(self.target_col)

            # 更新数值列中的目标列索引
            if self.target_col in self.numeric_cols:
                 self.target_col_index_in_numeric = self.numeric_cols.index(self.target_col)
                 logger.info(f"目标列索引已更新为: {self.target_col_index_in_numeric}")

                 # 当目标列改变时，当前模型不再有效，需要重新训练
                 if self.model:
                      logger.warning("目标列已更改。之前训练/加载的模型不再兼容。需要重新训练。")
                      # 禁用预测和评估按钮
                      self.btn_predict_single.setEnabled(False)
                      self.btn_predict_24h.setEnabled(False)
                      self.btn_evaluate_loaded.setEnabled(False)
                      self.oracle_mode_checkbox.setEnabled(False)
                      # 显示警告消息
                      QMessageBox.warning(self, "目标列已更改",
                                         "您已更改目标列，当前加载的模型不再兼容。\n\n"
                                         "请重新训练模型以预测新的目标列。")
                      # 重置模型，防止误用不兼容的模型
                      self.model = None
                      self.current_model_id = None
                      self.current_model_config = {}
                      # 更新模型目标标签
                      self.model_target_label.setText("当前模型: 未加载")
                      self.model_target_label.setStyleSheet("font-weight: bold; color: #555;")
                      # 启用训练按钮
                      self.btn_train.setEnabled(True)
            else:
                 logger.error(f"选定的目标列 '{self.target_col}' 不在归一化的数值列中！")
                 self.target_col_index_in_numeric = None
                 QMessageBox.critical(self, "错误", f"选定的目标列 '{self.target_col}' 不是有效的数值型或未被归一化，无法用作目标。")
                 # 禁用训练
                 self.btn_train.setEnabled(False)


            # 更新数据概览图以显示新目标
            self.show_data_overview()
            self.update_status_bar(f"目标列已更新为: {self.target_col}", 5000)

        except Exception as e:
             logger.error(f"Error updating state after target column change: {e}", exc_info=True)
             QMessageBox.critical(self, "错误", f"更新目标列时出错: {e}")

    def _update_feature_columns(self):
        """更新特征列列表，确保包含所有重要特征，包括导数特征。"""
        if self.data is not None:
            # 使用归一化数据框中的列
            all_cols = self.data.columns.tolist()
            if self.target_col in all_cols:
                # 排除目标列，但包含所有其他列作为特征
                self.feature_cols = [col for col in all_cols if col != self.target_col]

                # 检查并记录重要的导数特征
                derivative_features = []
                lag_features = []
                rolling_features = []

                # 检查目标列的导数特征是否存在并包含在特征列表中
                target_derivatives = [
                    f"{self.target_col}_change",
                    f"{self.target_col}_acceleration",
                    f"{self.target_col}_pct_change"
                ]

                for feature in target_derivatives:
                    if feature in self.feature_cols:
                        derivative_features.append(feature)
                    elif feature in all_cols:
                        # 如果特征存在但不在特征列表中，添加它
                        self.feature_cols.append(feature)
                        derivative_features.append(feature)
                        logger.info(f"添加导数特征到模型输入: {feature}")

                # 检查滞后特征
                for col in self.feature_cols:
                    if "_lag" in col:
                        lag_features.append(col)
                    elif any(stat in col for stat in ["_mean", "_std", "_max", "_min", "_skew"]):
                        rolling_features.append(col)

                # 更新特征索引映射
                self.feature_col_indices_map = {col: i for i, col in enumerate(self.feature_cols)}

                # 记录特征统计信息
                logger.info(f"特征列已更新 (排除 '{self.target_col}'). 总数: {len(self.feature_cols)}")
                if derivative_features:
                    logger.info(f"包含的导数特征: {derivative_features}")
                if lag_features:
                    logger.info(f"包含的滞后特征: {len(lag_features)} 个")
                if rolling_features:
                    logger.info(f"包含的滚动统计特征: {len(rolling_features)} 个")

                # 如果模型存在，检查其输入维度是否与新特征计数匹配
                if self.model and self.model.input_dim != len(self.feature_cols):
                    logger.warning(f"模型输入维度 ({self.model.input_dim}) 与新特征数量 ({len(self.feature_cols)}) 不匹配。模型无效。")
            else:
                logger.error(f"在更新特征列时未找到目标列 '{self.target_col}'。")
                self.feature_cols = None
                self.feature_col_indices_map = {}
        else:
            self.feature_cols = None
            self.feature_col_indices_map = {}

    def set_controls_enabled(self, enabled: bool):
        """启用或禁用界面上的主要控件"""
        # 数据加载按钮始终启用（或单独处理）
        # self.btn_load.setEnabled(enabled)
        self.target_column_selector.setEnabled(enabled and self.data is not None)
        self.model_selector.setEnabled(enabled)
        self.seq_length_selector.setEnabled(enabled)
        # 根据可见性启用特定的超参数小部件，而不是仅仅是全局标志
        # self.update_hyperparameter_ui() # 改为调用？
        for _, widget in self.hp_widgets.values():
            widget.setEnabled(enabled)
        # 重新应用启用/禁用后的可见性规则
        if enabled: self.update_hyperparameter_ui()

        self.epoch_selector.setEnabled(enabled)
        self.early_stop_spin.setEnabled(enabled)
        self.early_stop_min_delta_spin.setEnabled(enabled)

        # 训练按钮还取决于其他状态
        self.btn_train.setEnabled(enabled and self.data is not None and not self.is_training)
        self.btn_stop_train.setEnabled(enabled and self.is_training)

        # 预测/评估按钮取决于模型和数据
        can_predict = enabled and self.model is not None and self.data is not None
        self.btn_predict_single.setEnabled(can_predict)
        self.btn_predict_24h.setEnabled(can_predict)
        self.oracle_mode_checkbox.setEnabled(can_predict)  # 与预测按钮联动
        self.btn_evaluate_loaded.setEnabled(can_predict)

        # 比较选项卡按钮取决于选择和启用状态
        self.model_table.setEnabled(enabled)
        self.btn_refresh_models.setEnabled(enabled)
        if enabled:
            self.on_model_table_selection_changed() # 更新比较按钮基于选择
        else:
            # 如果全局状态被禁用，禁用所有比较操作按钮
            self.btn_view_logs.setEnabled(False)
            self.btn_compare_logs.setEnabled(False)
            self.btn_compare_metrics.setEnabled(False)
            self.btn_load_selected_model.setEnabled(False)
            self.btn_delete_selected_model.setEnabled(False)


    def show_data_overview(self):
        """显示数据概览图（调整垂直间距）。""" # 更新文档字符串
        logger.info("正在更新数据概览图...")
        self.data_fig.clf()

        if self.data is None or self.data.empty:
            # Display a message if no data is loaded
            ax = self.data_fig.add_subplot(111)
            ax.text(0.5, 0.5, '请先加载数据...', horizontalalignment='center',
                    verticalalignment='center', transform=ax.transAxes, fontsize=14, color='gray')
            ax.set_xticks([])
            ax.set_yticks([])
            self.data_canvas.draw()
            return

        # --- Create plot layout using GridSpec ---
        # More flexible layout: 3 rows - Time series, Distributions, Scatter/Prediction
        gs = self.data_fig.add_gridspec(3, 2, height_ratios=[3, 2, 2], hspace=0.8, wspace=0.3)
        ax_ts = self.data_fig.add_subplot(gs[0, :]) # Top row: Time series
        ax_hist1 = self.data_fig.add_subplot(gs[1, 0]) # Middle row, left: Hist 1
        ax_hist2 = self.data_fig.add_subplot(gs[1, 1]) # Middle row, right: Hist 2
        ax_scatter = self.data_fig.add_subplot(gs[2, 0]) # Bottom row, left: Scatter

        # --- Draw static overview content ---
        try:
             self.draw_data_overview_content(ax_ts, ax_hist1, ax_hist2, ax_scatter)
        except Exception as e:
            logger.error(f"绘制静态数据概览时出错: {e}", exc_info=True)
            ax_ts.text(0.5, 0.5, '绘制数据概览时出错', ha='center', va='center', color='red')

        # --- 最后调整 ---
        self.data_fig.suptitle('数据概览', fontsize=14, y=0.98)

        # --- 布局调整
        try:
            self.data_fig.tight_layout(pad=1.5, h_pad=0.5)
            logger.info("已应用 tight_layout 调整布局。")
        except Exception as e:
            logger.warning(f"调用 tight_layout 时出错: {e}")

        self.data_canvas.draw()  # 绘制更新后的画布
        logger.info("数据概览图已更新。")


    def draw_data_overview_content(self, ax_ts, ax_hist1, ax_hist2, ax_scatter):
        """绘制数据概览图表的静态部分 (时序, 分布, 散点)。"""
        if self.data is None or self.data.empty: return

        # 检查新的scalers字典或旧的scaler对象
        if (not hasattr(self, 'scalers') or self.scalers is None) and (not hasattr(self, 'scaler') or self.scaler is None):
            logger.warning("Cannot draw detailed overview: Scalers missing.")
            ax_ts.text(0.5, 0.5, '无法绘制详细概览\n缺少归一化信息', ha='center', va='center', color='orange')
            return

        # 检查特征列信息
        if (not hasattr(self, 'minmax_cols') or not self.minmax_cols) and (not hasattr(self, 'numeric_cols') or not self.numeric_cols):
            logger.warning("Cannot draw detailed overview: Feature columns information missing.")
            ax_ts.text(0.5, 0.5, '无法绘制详细概览\n缺少特征列信息', ha='center', va='center', color='orange')
            return

        # --- 1. Target Variable Time Series ---
        target_col = self.target_col
        plot_len = min(len(self.data), 3000) # Limit points for performance
        target_data_scaled = self.data[target_col].iloc[-plot_len:]
        target_timestamps = self.data.index[-plot_len:]

        # Plot scaled data
        ax_ts.plot(target_timestamps, target_data_scaled, color='#1f77b4', linewidth=1, label='归一化值')
        ax_ts.set_ylabel(f'归一化 {target_col}', fontsize=9, color='#1f77b4')
        ax_ts.tick_params(axis='y', labelcolor='#1f77b4', labelsize=8)
        # ax_ts.set_xlabel('时间', fontsize=9)
        ax_ts.grid(True, linestyle='--', alpha=0.6)
        ax_ts.set_title(f'{target_col} 时序图 (最近 {plot_len} 条)', fontsize=11)

        # Add secondary axis for actual (unscaled) values
        try:
            # 确定目标列在哪个归一化组中
            if hasattr(self, 'minmax_cols') and target_col in self.minmax_cols:
                target_index_in_numeric = self.minmax_cols.index(target_col)
                scaler_to_use = self.scalers.get('minmax_scaler')
                cols_to_use = self.minmax_cols
            elif hasattr(self, 'robust_cols') and target_col in self.robust_cols:
                target_index_in_numeric = self.robust_cols.index(target_col)
                scaler_to_use = self.scalers.get('robust_scaler')
                cols_to_use = self.robust_cols
            elif hasattr(self, 'numeric_cols') and target_col in self.numeric_cols:
                # 兼容旧版本
                target_index_in_numeric = self.numeric_cols.index(target_col)
                scaler_to_use = self.scaler
                cols_to_use = self.numeric_cols
            else:
                raise ValueError(f"Target column {target_col} not found in any normalized column group")

            ax_ts_right = ax_ts.twinx()
            ymin_s, ymax_s = ax_ts.get_ylim()

            # 创建用于反向变换的虚拟数据
            if scaler_to_use is not None and cols_to_use:
                dummy_limits = np.zeros((2, len(cols_to_use)))
                dummy_limits[0, target_index_in_numeric] = ymin_s
                dummy_limits[1, target_index_in_numeric] = ymax_s
                actual_vals_range = scaler_to_use.inverse_transform(dummy_limits)[:, target_index_in_numeric]
                ax_ts_right.set_ylim(actual_vals_range[0], actual_vals_range[1])
                ax_ts_right.set_ylabel(f'实际 {target_col}', fontsize=9, color='#ff7f0e')
                ax_ts_right.tick_params(axis='y', labelcolor='#ff7f0e', labelsize=8)

                # 获取原始列名用于标签
                original_target_col_name = next((k for k, v in COLUMN_MAPPING.items() if v == target_col), target_col)

                # 添加数据类型信息
                data_type_str = DATA_TYPES.get(CURRENT_DATA_TYPE, "时序数据")
                ax_ts_right.set_ylabel(f'实际 {target_col} ({original_target_col_name})\n[{data_type_str}]', fontsize=9, color='#ff7f0e')
            else:
                logger.warning(f"Could not find appropriate scaler for {target_col}")
                ax_ts_right.set_ylabel(f'{target_col} (归一化值)', fontsize=9, color='#ff7f0e')

        except (ValueError, IndexError, AttributeError) as e:
            logger.warning(f"Could not create secondary axis for actual values: {e}")

        # Format X axis dates nicely
        ax_ts.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
        # ax_ts.figure.autofmt_xdate(rotation=15, ha='right')
        # 手动设置刻度标签旋转
        for label in ax_ts.get_xticklabels():
            label.set_rotation(30)  # 尝试不同的旋转角度，例如 30 度
            label.set_ha('right')  # 设置水平对齐方式为右对齐

        # --- 2. Feature Distributions (Example: Humidity and Pressure) ---
        hist_cols = ['humidity', 'pressure']
        axes_hist = [ax_hist1, ax_hist2]
        colors_hist = ['#2ca02c', '#d62728']

        for i, col_name in enumerate(hist_cols):
            ax_h = axes_hist[i]

            # 确定列在哪个归一化组中
            if hasattr(self, 'minmax_cols') and col_name in self.minmax_cols and col_name in self.data.columns:
                try:
                    col_idx = self.minmax_cols.index(col_name)
                    scaler_to_use = self.scalers.get('minmax_scaler')
                    cols_to_use = self.minmax_cols

                    # 获取未归一化的数据用于直方图
                    # 注意：对整个数据集进行inverse_transform可能会很慢
                    # 如果性能是问题，考虑采样
                    all_scaled = self.data[cols_to_use].values
                    all_unscaled_data = scaler_to_use.inverse_transform(all_scaled)
                    col_actual = all_unscaled_data[:, col_idx]

                    original_col_name = next((k for k, v in COLUMN_MAPPING.items() if v == col_name), col_name)
                    ax_h.hist(col_actual, bins=30, color=colors_hist[i], edgecolor='white', alpha=0.75, density=True)
                    ax_h.set_title(f'{col_name} 实际值分布', fontsize=10)
                    ax_h.set_xlabel(f'{original_col_name}', fontsize=9)
                    ax_h.set_ylabel('密度', fontsize=9)
                    ax_h.tick_params(axis='both', labelsize=8)
                    ax_h.grid(True, linestyle=':', alpha=0.5)
                    # 添加均值/标准差文本
                    mean_val = np.mean(col_actual)
                    std_val = np.std(col_actual)
                    ax_h.text(0.95, 0.95, f'均值: {mean_val:.2f}\n标准差: {std_val:.2f}',
                              transform=ax_h.transAxes, ha='right', va='top', fontsize=8,
                              bbox=dict(boxstyle='round,pad=0.3', fc='white', alpha=0.7))

                except Exception as e:
                    logger.warning(f"Could not plot histogram for {col_name} (minmax): {e}")
                    ax_h.text(0.5, 0.5, f'无法绘制\n{col_name} 分布', ha='center', va='center', color='gray')

            elif hasattr(self, 'robust_cols') and col_name in self.robust_cols and col_name in self.data.columns:
                try:
                    col_idx = self.robust_cols.index(col_name)
                    scaler_to_use = self.scalers.get('robust_scaler')
                    cols_to_use = self.robust_cols

                    all_scaled = self.data[cols_to_use].values
                    all_unscaled_data = scaler_to_use.inverse_transform(all_scaled)
                    col_actual = all_unscaled_data[:, col_idx]

                    original_col_name = next((k for k, v in COLUMN_MAPPING.items() if v == col_name), col_name)
                    ax_h.hist(col_actual, bins=30, color=colors_hist[i], edgecolor='white', alpha=0.75, density=True)
                    ax_h.set_title(f'{col_name} 实际值分布', fontsize=10)
                    ax_h.set_xlabel(f'{original_col_name}', fontsize=9)
                    ax_h.set_ylabel('密度', fontsize=9)
                    ax_h.tick_params(axis='both', labelsize=8)
                    ax_h.grid(True, linestyle=':', alpha=0.5)
                    # 添加均值/标准差文本
                    mean_val = np.mean(col_actual)
                    std_val = np.std(col_actual)
                    ax_h.text(0.95, 0.95, f'均值: {mean_val:.2f}\n标准差: {std_val:.2f}',
                              transform=ax_h.transAxes, ha='right', va='top', fontsize=8,
                              bbox=dict(boxstyle='round,pad=0.3', fc='white', alpha=0.7))

                except Exception as e:
                    logger.warning(f"Could not plot histogram for {col_name} (robust): {e}")
                    ax_h.text(0.5, 0.5, f'无法绘制\n{col_name} 分布', ha='center', va='center', color='gray')

            # 兼容旧版本
            elif hasattr(self, 'numeric_cols') and col_name in self.numeric_cols and col_name in self.data.columns:
                try:
                    col_idx_numeric = self.numeric_cols.index(col_name)
                    # 获取未归一化的数据用于直方图
                    all_scaled_numeric = self.data[self.numeric_cols].values
                    all_unscaled_data = self.scaler.inverse_transform(all_scaled_numeric)
                    col_actual = all_unscaled_data[:, col_idx_numeric]

                    original_col_name = next((k for k, v in COLUMN_MAPPING.items() if v == col_name), col_name)
                    ax_h.hist(col_actual, bins=30, color=colors_hist[i], edgecolor='white', alpha=0.75, density=True)
                    ax_h.set_title(f'{col_name} 实际值分布', fontsize=10)
                    ax_h.set_xlabel(f'{original_col_name}', fontsize=9)
                    ax_h.set_ylabel('密度', fontsize=9)
                    ax_h.tick_params(axis='both', labelsize=8)
                    ax_h.grid(True, linestyle=':', alpha=0.5)
                    # 添加均值/标准差文本
                    mean_val = np.mean(col_actual)
                    std_val = np.std(col_actual)
                    ax_h.text(0.95, 0.95, f'均值: {mean_val:.2f}\n标准差: {std_val:.2f}',
                              transform=ax_h.transAxes, ha='right', va='top', fontsize=8,
                              bbox=dict(boxstyle='round,pad=0.3', fc='white', alpha=0.7))
                except Exception as e:
                     logger.warning(f"Could not plot histogram for {col_name}: {e}")
                     ax_h.text(0.5, 0.5, f'无法绘制\n{col_name} 分布', ha='center', va='center', color='gray')
            else:
                ax_h.text(0.5, 0.5, f'{col_name} 数据\n不可用', ha='center', va='center', color='gray')
                ax_h.set_title(f'{col_name} 分布 (不可用)', fontsize=10)


        # --- 3. Scatter Plot (Example: Temperature vs. Wind Speed, colored by Humidity) ---
        scatter_x_col = 'wind_speed'
        scatter_y_col = 'temperature' # Use 'temperature' even if it's the target for overview
        scatter_color_col = 'humidity'

        # 检查所有散点图所需的列是否都在同一个归一化组中
        cols_in_minmax = all(c in self.minmax_cols for c in [scatter_x_col, scatter_y_col, scatter_color_col]) if hasattr(self, 'minmax_cols') else False
        cols_in_robust = all(c in self.robust_cols for c in [scatter_x_col, scatter_y_col, scatter_color_col]) if hasattr(self, 'robust_cols') else False
        cols_in_numeric = all(c in self.numeric_cols for c in [scatter_x_col, scatter_y_col, scatter_color_col]) if hasattr(self, 'numeric_cols') else False

        if cols_in_minmax or cols_in_robust or cols_in_numeric:
            try:
                if cols_in_minmax:
                    x_idx = self.minmax_cols.index(scatter_x_col)
                    y_idx = self.minmax_cols.index(scatter_y_col)
                    color_idx = self.minmax_cols.index(scatter_color_col)

                    # 采样数据用于散点图
                    sample_size = min(5000, len(self.data))
                    sample_indices = np.random.choice(len(self.data), sample_size, replace=False)
                    sampled_scaled = self.data[self.minmax_cols].iloc[sample_indices].values
                    all_unscaled_data = self.scalers.get('minmax_scaler').inverse_transform(sampled_scaled)

                    x_actual = all_unscaled_data[:, x_idx]
                    y_actual = all_unscaled_data[:, y_idx]
                    color_actual = all_unscaled_data[:, color_idx]

                elif cols_in_robust:
                    x_idx = self.robust_cols.index(scatter_x_col)
                    y_idx = self.robust_cols.index(scatter_y_col)
                    color_idx = self.robust_cols.index(scatter_color_col)

                    # 采样数据用于散点图
                    sample_size = min(5000, len(self.data))
                    sample_indices = np.random.choice(len(self.data), sample_size, replace=False)
                    sampled_scaled = self.data[self.robust_cols].iloc[sample_indices].values
                    all_unscaled_data = self.scalers.get('robust_scaler').inverse_transform(sampled_scaled)

                    x_actual = all_unscaled_data[:, x_idx]
                    y_actual = all_unscaled_data[:, y_idx]
                    color_actual = all_unscaled_data[:, color_idx]

                # 兼容旧版本
                elif cols_in_numeric:
                    x_idx = self.numeric_cols.index(scatter_x_col)
                    y_idx = self.numeric_cols.index(scatter_y_col)
                    color_idx = self.numeric_cols.index(scatter_color_col)

                    # 重用直方图计算的未归一化数据，否则重新计算/加载子集
                    if 'all_unscaled_data' not in locals(): # 如果直方图失败或使用了不同的列
                        # 如果数据集很大，对散点图进行采样
                        sample_size = min(5000, len(self.data))
                        sample_indices = np.random.choice(len(self.data), sample_size, replace=False)
                        sampled_scaled_numeric = self.data[self.numeric_cols].iloc[sample_indices].values
                        all_unscaled_data = self.scaler.inverse_transform(sampled_scaled_numeric)
                    else:
                        # 如果已经计算过，可能需要从中采样
                        if len(all_unscaled_data) > 5000:
                            sample_indices = np.random.choice(len(all_unscaled_data), 5000, replace=False)
                            all_unscaled_data = all_unscaled_data[sample_indices]

                    x_actual = all_unscaled_data[:, x_idx]
                    y_actual = all_unscaled_data[:, y_idx]
                    color_actual = all_unscaled_data[:, color_idx]

                # 获取原始列名
                original_x_name = next((k for k, v in COLUMN_MAPPING.items() if v == scatter_x_col), scatter_x_col)
                original_y_name = next((k for k, v in COLUMN_MAPPING.items() if v == scatter_y_col), scatter_y_col)
                original_color_name = next((k for k, v in COLUMN_MAPPING.items() if v == scatter_color_col), scatter_color_col)

                # 绘制散点图
                scatter = ax_scatter.scatter(x_actual, y_actual, c=color_actual, cmap='viridis', s=10, alpha=0.6)
                cbar = self.data_fig.colorbar(scatter, ax=ax_scatter, aspect=15, pad=0.02) # 调整颜色条大小
                cbar.set_label(f'{original_color_name}', fontsize=9)
                cbar.ax.tick_params(labelsize=8)
                ax_scatter.set_title(f'{scatter_y_col} vs {scatter_x_col}', fontsize=10)
                ax_scatter.set_xlabel(f'{original_x_name}', fontsize=9)
                ax_scatter.set_ylabel(f'{original_y_name}', fontsize=9)
                ax_scatter.tick_params(axis='both', labelsize=8)
                ax_scatter.grid(True, linestyle=':', alpha=0.5)

            except Exception as e:
                logger.warning(f"Could not plot scatter plot: {e}")
                ax_scatter.text(0.5, 0.5, '无法绘制散点图', ha='center', va='center', color='gray')
        else:
            ax_scatter.text(0.5, 0.5, '散点图所需数据\n(风速/温度/湿度)\n不完全可用', ha='center', va='center', color='gray')
            ax_scatter.set_title('散点图 (数据不可用)', fontsize=10)


    def start_training(self):
        """启动模型训练过程，使用 QThread。"""
        logger.info("--- 初始化训练序列 ---")
        if self.data is None or self.data.empty:
            QMessageBox.warning(self, "警告", "请先成功加载并处理数据！")
            return
        if self.is_training:
             QMessageBox.warning(self, "警告", "另一个训练任务已在进行中。")
             return

        logger.info("在开始新训练前清除之前的训练图表...")
        try:
            self.loss_ax.clear()
            if self.lr_ax:
                self.lr_ax.clear()
                self.lr_ax.get_yaxis().set_visible(False)

            if not hasattr(self, 'lr_ax') or self.lr_ax is None:  # 确保 lr_ax 存在
                self.lr_ax = self.loss_ax.twinx()

            # 重新设置坐标轴和空的线对象
            self.loss_ax.set_title('训练过程监控 (等待开始...)')
            self.loss_ax.set_xlabel('训练轮次')
            self.loss_ax.set_ylabel('损失值 (MSE)')
            self.loss_ax.grid(True)
            self.train_loss_line, = self.loss_ax.plot([], [], 'b-', marker='.', markersize=3, label='训练损失')
            self.val_loss_line, = self.loss_ax.plot([], [], 'r-', marker='.', markersize=3, label='验证损失')
            self.lr_line, = self.lr_ax.plot([], [], 'g:', markersize=3, label='学习率')
            self.lr_ax.set_ylabel('学习率', color='g', fontsize=9)
            self.lr_ax.tick_params(axis='y', labelcolor='g', labelsize=8)
            self.lr_ax.get_yaxis().set_visible(False)  # 初始隐藏
            self.update_training_plot_legend()
            self.train_canvas.draw_idle()  # 立即绘制空的图表
            logger.info("之前的训练图表已清除。")
        except Exception as e:
            logger.error(f"清除之前的训练图表时出错: {e}", exc_info=True)

        # UI更新和状态
        self.is_training = True
        self.set_controls_enabled(False) # 在训练期间禁用大多数控件
        self.btn_stop_train.setEnabled(True) # 明确启用停止按钮
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)
        self.progress_bar.setFormat("准备中...")
        self.current_model_id = None # 为新训练重置模型ID
        self.model = None # 清除之前的模型实例
        self.current_model_config = {} # 为新训练重置配置
        self.update_status_bar("开始准备训练...", 0) # 持久消息
        self.tab_widget.setCurrentWidget(self.tab_train_monitor) # 切换到监控选项卡
        # 清除之前的训练图表
        self.loss_ax.clear()
        if self.lr_ax: self.lr_ax.clear(); self.lr_ax.get_yaxis().set_visible(False) # 同时清除LR轴
        self.loss_ax.set_title('训练过程监控 (等待开始...)')
        self.loss_ax.set_xlabel('训练轮次')
        self.loss_ax.set_ylabel('损失值 (MSE)')
        self.loss_ax.grid(True)
        self.train_loss_line, = self.loss_ax.plot([], [], 'b-', marker='.', markersize=3, label='训练损失')
        self.val_loss_line, = self.loss_ax.plot([], [], 'r-', marker='.', markersize=3, label='验证损失')
        # 设置学习率的次要轴
        self.lr_ax = self.loss_ax.twinx()
        self.lr_line, = self.lr_ax.plot([], [], 'g:', markersize=3, label='学习率')
        self.lr_ax.set_ylabel('学习率', color='g', fontsize=9)
        self.lr_ax.tick_params(axis='y', labelcolor='g', labelsize=8)
        self.lr_ax.get_yaxis().set_visible(False) # 初始隐藏，直到有LR数据
        # 设置组合图例
        self.update_training_plot_legend()
        self.train_canvas.draw()
        QApplication.processEvents() # 确保UI更新

        logger.info("UI状态已准备好进行训练。")

        try:
            # 1. 收集超参数
            logger.info("1. 从UI读取超参数...")
            model_type = self.model_selector.currentText()
            seq_length = self.seq_length_selector.value()
            self.seq_length = seq_length # 如果更改了则更新实例变量
            epochs_requested = self.epoch_selector.value()
            lr = self.lr_double_spin.value()
            dropout = self.dropout_double_spin.value()
            early_stopping_patience = self.early_stop_spin.value() if self.early_stop_spin.value() > 0 else None # 使用None禁用
            # 从UI获取最小改善阈值参数
            early_stopping_min_delta = self.early_stop_min_delta_spin.value()
            logger.info(f"   模型类型: {model_type}, 序列长度: {seq_length}, 轮次: {epochs_requested}, 学习率: {lr:.6f}")
            logger.info(f"   Dropout: {dropout}, 早停耐心: {early_stopping_patience}")
            logger.info(f"   早停最小改善阈值: {early_stopping_min_delta}")

            # 根据模型类型构建配置字典
            config = {'dropout': dropout} # 通用参数
            if model_type in ['LSTM', 'GRU']:
                config['hidden_dim'] = self.hidden_dim_spin.value()
                config['num_layers'] = self.num_layers_spin.value()
                logger.info(f"   RNN配置: 隐藏层维度={config['hidden_dim']}, 层数={config['num_layers']}")
            elif model_type == 'Transformer':
                config['hidden_dim'] = self.hidden_dim_spin.value()
                config['num_layers'] = self.num_layers_spin.value()
                config['nhead'] = self.nhead_spin.value()
                logger.info(f"   Transformer配置: 隐藏层维度={config['hidden_dim']}, 层数={config['num_layers']}, 头数={config['nhead']}")
                # 在继续之前验证nhead的可除性
                if config['hidden_dim'] % config['nhead'] != 0:
                    raise ValueError(f"Transformer 配置错误: 隐藏层维度 ({config['hidden_dim']}) 必须能被头数 ({config['nhead']}) 整除。")
            elif model_type == 'TCN':
                tcn_channels_str = self.tcn_channels_edit.text()
                try:
                    # 更健壮的解析：允许空格，过滤空字符串
                    tcn_num_channels = [int(x.strip()) for x in tcn_channels_str.split(',') if x.strip()]
                    if not tcn_num_channels or any(c <= 0 for c in tcn_num_channels):
                         raise ValueError("通道数必须是正整数列表。")
                    config['tcn_num_channels'] = tcn_num_channels
                except ValueError as e:
                    raise ValueError(f"TCN 通道数格式无效: '{tcn_channels_str}'. 请使用逗号分隔的正整数 (例如 32,64,128)。错误: {e}")
                config['tcn_kernel_size'] = self.tcn_kernel_spin.value()
                logger.info(f"   TCN配置: 通道={config['tcn_num_channels']}, 卷积核大小={config['tcn_kernel_size']}")

            self.current_model_config = config.copy() # 存储用于此次运行的配置
            logger.info("   超参数读取并成功创建配置。")

            # 2. 准备数据加载器
            logger.info("2. 准备数据加载器...")
            self.update_status_bar("准备数据加载器...", 0)
            QApplication.processEvents()

            # 确保特征列是最新的（以防目标改变）
            self._update_feature_columns()
            if not self.feature_cols:
                 raise ValueError("无法确定用于训练的特征列。")
            if self.target_col not in self.data.columns:
                 raise ValueError(f"目标列 '{self.target_col}' 在最终数据中找不到。")

            # 使用正确的列提取NumPy数组
            X = self.data[self.feature_cols].values.astype(np.float32)
            y = self.data[self.target_col].values.astype(np.float32)
            logger.info(f"   用于序列化的数据形状: X={X.shape}, y={y.shape}")

            # 创建序列
            num_samples = len(X) - seq_length
            if num_samples <= 0:
                raise ValueError(f"数据量不足 ({len(X)} points) 无法创建长度为 {seq_length} 的序列。请减少序列长度或使用更多数据。")

            # 高效序列创建（避免大数据的列表推导式）
            # 如果内存允许且数据量大，使用stride_tricks，否则使用更安全的方法
            # Stride tricks示例（可能更快但可读性较差，小心内存视图）
            # from numpy.lib.stride_tricks import as_strided
            # itemsize = X.itemsize
            # X_seq = as_strided(X, shape=(num_samples, seq_length, X.shape[1]), strides=(X.strides[0], X.strides[0], X.strides[1]))
            # itemsize_y = y.itemsize
            # 根据y是1D还是2D原始调整形状
            # y_target_shape = (num_samples,) if y.ndim == 1 else (num_samples, y.shape[1])
            # y_seq = as_strided(y[seq_length:], shape=y_target_shape, strides=(y.strides[0],) * y.ndim)
            # 更安全的列表推导式方法
            X_seq = np.array([X[i : i + seq_length] for i in range(num_samples)], dtype=np.float32)
            y_seq = np.array([y[i + seq_length] for i in range(num_samples)], dtype=np.float32)
            # 确保y_seq是2D [num_samples, output_dim]，如果output_dim > 1（这里output_dim是1）
            if y_seq.ndim == 1: y_seq = y_seq[:, np.newaxis]
            logger.info(f"   序列形状: X_seq={X_seq.shape}, y_seq={y_seq.shape}")


            # 分割数据（考虑时间序列的基于时间的分割）
            # 目前使用简单的比例分割
            split_ratio = 0.8
            split_idx = int(split_ratio * num_samples)
            if split_idx <= 0 or split_idx >= num_samples:
                raise ValueError(f"数据量太少 ({num_samples} sequences) 无法进行有效的训练/验证集分割。")

            X_train, X_val = X_seq[:split_idx], X_seq[split_idx:]
            y_train, y_val = y_seq[:split_idx], y_seq[split_idx:]
            logger.info(f"   训练/验证集分割: 训练={X_train.shape[0]} 样本, 验证={X_val.shape[0]} 样本")
            if X_train.shape[0] == 0 or X_val.shape[0] == 0:
                 raise ValueError("训练集或验证集为空，无法继续。")


            # 创建张量和数据集 将 NumPy 数组转换为 PyTorch 张量。
            # 这使得数据可以直接用于 PyTorch 模型进行训练和验证
            X_train_tensor = torch.from_numpy(X_train)
            y_train_tensor = torch.from_numpy(y_train) # 形状 [n_train, 1]
            X_val_tensor = torch.from_numpy(X_val)
            y_val_tensor = torch.from_numpy(y_val) # 形状 [n_val, 1]

            # 将输入和目标打包在一起，便于后续的批处理和迭代
            train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
            val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
            logger.info("   TensorDatasets已创建。")


            # 创建DataLoaders
            batch_size = 16 # 批量大小可配置？
            num_workers = 0 # 除非性能至关重要，否则保持为0以保持稳定性
            pin_memory_flag = torch.cuda.is_available() # 如果CUDA可用则使用
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True,
                                      num_workers=num_workers, pin_memory=pin_memory_flag, drop_last=True) # drop_last可以帮助稳定性
            val_loader = DataLoader(val_dataset, batch_size=batch_size * 2, shuffle=False,
                                    num_workers=num_workers, pin_memory=pin_memory_flag)
            logger.info(f"   DataLoaders已创建。批量大小={batch_size}, 工作线程数={num_workers}, 固定内存={pin_memory_flag}")


            # 3. 实例化模型
            logger.info("3. 实例化时间序列模型...")
            self.update_status_bar("创建模型实例...", 0)
            QApplication.processEvents()

            input_dim = X_train_tensor.shape[2] # 从实际数据获取输入维度
            output_dim = y_train_tensor.shape[1] # 从实际数据获取输出维度（这里应该是1）
            self.model = TimeSeriesModel(
                model_type=model_type,
                input_dim=input_dim,
                output_dim=output_dim,
                config=self.current_model_config,
                use_builtin_rnn=False # 允许使用自定义架构（如果可用）（可以稍后添加UI切换）
            )
            logger.info(f"   模型实例已创建: {type(self.model.core_model).__name__}")
            # 记录模型结构详情？
            # logger.info(f"   模型详情:\n{self.model}")


            # 4. 创建并启动训练线程
            logger.info("4. 准备并启动训练线程...")
            self.update_status_bar("启动训练线程...", 0)
            QApplication.processEvents()

            # 定义准则（损失函数）
            criterion = nn.MSELoss() # 或者使其可配置

            # 准备回调
            callbacks = [
                # 如果需要，在这里添加任何特定于应用程序的回调，
                # 例如，直接更新UI元素的回调（如果可能，使用信号代替）
            ]

            self.train_thread = TrainingThread(
                model=self.model,
                train_loader=train_loader,
                val_loader=val_loader,
                epochs=epochs_requested,
                lr=lr,
                early_stopping_patience=early_stopping_patience,
                early_stopping_min_delta=early_stopping_min_delta,  # 传递最小改善阈值参数
                device=self.get_selected_device(), # 使用辅助函数获取设备字符串
                criterion=criterion,
                callbacks=callbacks,
                parent=self # 设置线程管理的父对象
            )

            # 连接线程信号到UI槽
            self.train_thread.update_progress.connect(self.update_training_plot_and_progress)
            self.train_thread.training_finished.connect(self.on_training_complete)
            self.train_thread.training_error.connect(self.on_training_error)
            self.train_thread.status_update.connect(self.update_status_bar_from_thread)
            self.train_thread.finished.connect(self.on_thread_finished) # 线程执行结束时的信号

            self.train_thread.start() # 启动后台训练
            logger.info("   训练线程已启动。")
            self.update_status_bar(f"开始使用 {model_type} 模型训练...", 0) # 更新状态


        except Exception as e:
            # 处理准备错误
            error_msg = f"训练准备失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            QMessageBox.critical(self, "训练准备错误", error_msg)
            self.update_status_bar(f"错误: {error_msg[:100]}...", 10000)
            # 重置UI状态
            self.is_training = False
            self.set_controls_enabled(True) # 重新启用控件
            self.progress_bar.setVisible(False)
            # 清除可能无效的模型状态
            self.model = None
            self.current_model_id = None
            self.current_model_config = {}

    def get_selected_device(self):
        """确定用于训练/预测的设备。"""
        # 为潜在的未来UI设备选择预留位置
        # 目前，如果可用则默认为CUDA，否则为CPU
        if torch.cuda.is_available():
            # 可以在这里添加多个GPU的检查
            return "cuda"
        else:
            return "cpu"

    def stop_training(self):
        """请求训练线程停止。"""
        # 修复变量名：使用self.train_thread而不是self.training_thread
        if hasattr(self, 'train_thread') and self.train_thread is not None:
            if self.train_thread.isRunning():
                logger.info("点击了停止训练按钮。请求线程停止。")
                self.btn_stop_train.setText("正在停止...")
                self.btn_stop_train.setEnabled(False) # 点击后禁用按钮
                self.train_thread.stop() # 向线程发送停止信号
                self.update_status_bar("正在尝试停止训练...", 0)
                logger.info("已向训练线程发送停止信号。")
                return
            else:
                logger.warning("训练线程存在但未运行。")
        else:
            logger.warning("点击了停止训练，但未找到训练线程对象。")

        # 如果到达这里，说明没有找到活动的训练线程
        QMessageBox.information(self, "提示", "当前没有正在进行的训练任务。")
        # 确保UI状态一致
        self.is_training = False
        self.set_controls_enabled(True)
        self.btn_stop_train.setText("停止训练")
        self.btn_stop_train.setEnabled(False)
        self.progress_bar.setVisible(False)


    def update_training_plot_and_progress(self, epoch, train_loss, val_loss, current_lr):
        """更新训练过程图表和进度条"""
        logger.debug(f"绘图更新: Epoch={epoch}, TrainLoss={train_loss:.6f}, ValLoss={val_loss:.6f}, LR={current_lr:.7f}") # 使用 debug 级别

        # 取消下面代码块的注释
        self.progress_bar.setValue(epoch)
        # 确保最大值正确，以防用户在训练中更改了 epoch_selector
        current_max_epochs = self.epoch_selector.value()
        self.progress_bar.setMaximum(current_max_epochs)
        self.progress_bar.setFormat(f"轮次 {epoch}/{current_max_epochs} - Val Loss: {val_loss:.4f}")

        # 更新绘图数据
        if not hasattr(self, 'train_loss_line') or not hasattr(self, 'val_loss_line') or not hasattr(self, 'lr_line'):
             logger.warning("绘图线条未初始化，无法更新绘图。")
             return

        # 获取当前数据并追加新数据点
        x_data = list(self.train_loss_line.get_xdata())
        train_y_data = list(self.train_loss_line.get_ydata())
        val_y_data = list(self.val_loss_line.get_ydata())
        lr_y_data = list(self.lr_line.get_ydata())

        # 避免重复添加同一个 epoch 的点（如果信号被错误地多次触发）
        if not x_data or x_data[-1] < epoch:
            x_data.append(epoch)
            train_y_data.append(train_loss)
            val_y_data.append(val_loss)
            lr_y_data.append(current_lr)
        elif x_data[-1] == epoch: # 如果是同一个 epoch，则更新最后的值
            train_y_data[-1] = train_loss
            val_y_data[-1] = val_loss
            lr_y_data[-1] = current_lr
        else: # epoch 比上一个点小，可能是一个新的训练开始了，清空数据
             logger.warning(f"Detected epoch regression ({epoch} < {x_data[-1]}). Resetting plot data.")
             x_data = [epoch]
             train_y_data = [train_loss]
             val_y_data = [val_loss]
             lr_y_data = [current_lr]


        self.train_loss_line.set_data(x_data, train_y_data)
        self.val_loss_line.set_data(x_data, val_y_data)
        self.lr_line.set_data(x_data, lr_y_data)

        # 动态调整 Y 轴
        all_losses = train_y_data + val_y_data
        # 过滤掉 NaN 和 Inf 值
        valid_losses = [l for l in all_losses if not math.isnan(l) and not math.isinf(l)]

        if valid_losses:
            min_loss = min(valid_losses)
            max_loss = max(valid_losses)
            # 添加一些边距，但避免过大
            padding = (max_loss - min_loss) * 0.1 if max_loss > min_loss else max(abs(min_loss * 0.1), 0.01)
            # 确保下限不大于上限
            y_lower = max(0, min_loss - padding) if min_loss >= 0 else min_loss - padding # 损失通常非负，但以防万一
            y_upper = max_loss + padding
            if y_upper > y_lower: # 防止设置无效的范围
                self.loss_ax.set_ylim(y_lower, y_upper)
            else:
                 self.loss_ax.autoscale_view(True, True, True) # Fallback to autoscale
        else:
            self.loss_ax.set_ylim(0, 1) # Default range if no valid data

        # LR 轴 (仅当 LR 变化时更新范围可能更有效)
        valid_lrs = [l for l in lr_y_data if not math.isnan(l) and not math.isinf(l) and l > 0]
        if valid_lrs:
             self.lr_ax.get_yaxis().set_visible(True)
             min_lr = min(valid_lrs)
             max_lr = max(valid_lrs)
             # 简单地设置一个基于最大值的范围，或者基于当前值
             # padding_lr = max_lr * 0.1
             # self.lr_ax.set_ylim(0, max_lr + padding_lr)
             # 或者只在第一次显示时设置，之后自动调整
             if len(valid_lrs) == 1:
                 self.lr_ax.set_ylim(min_lr * 0.8, max_lr * 1.2)
             else:
                 self.lr_ax.relim() # 让 matplotlib 重新计算范围
                 self.lr_ax.autoscale_view(True, False, True) # 仅自动缩放 Y 轴
        else:
             self.lr_ax.get_yaxis().set_visible(False)

        # 重新计算 X 轴范围并绘制
        self.loss_ax.relim() # 考虑所有数据点重新计算 x 轴范围
        self.loss_ax.autoscale_view(True, True, False) # 自动缩放 X 轴, Y轴上面已处理
        self.update_training_plot_legend() # 更新图例
        try:
            # 使用 draw_idle() 可能更流畅，它会在 Qt 事件循环空闲时绘制
            self.train_canvas.draw_idle()
        except Exception as draw_err:
             logger.error(f"Error calling train_canvas.draw_idle(): {draw_err}")

    def update_training_plot_legend(self):
        """更新训练图表的图例，处理多个坐标轴。"""
        lines_loss, labels_loss = self.loss_ax.get_legend_handles_labels()
        lines_lr, labels_lr = self.lr_ax.get_legend_handles_labels()
        # 仅当轴可见时添加LR图例
        if self.lr_ax.get_yaxis().get_visible():
             self.loss_ax.legend(lines_loss + lines_lr, labels_loss + labels_lr, loc='best', fontsize='small')
        else:
             self.loss_ax.legend(lines_loss, labels_loss, loc='best', fontsize='small')

    def on_training_complete(self, log_history, final_val_loss, epochs_run,
                             min_val_loss, epoch_at_min, final_train_loss,
                             total_time_sec, params, device):
        logger.info("Training finished signal received. (Step 2: DB Restore)")  # 更新日志标记
        self.is_training = False

        # 恢复UI相关部分（保持这些恢复状态）
        total_time_str = time.strftime("%H:%M:%S", time.gmtime(total_time_sec)) if total_time_sec >= 0 else "N/A"
        result_message = (
            f"模型训练完成！\n"
            f"模型类型: {self.model.original_model_type if self.model else 'N/A'}\n"
            f"运行轮次: {epochs_run}/{self.epoch_selector.value()}\n"
            f"最终训练损失: {final_train_loss:.6f}\n"
            f"最终验证损失: {final_val_loss:.6f}\n"
            f"最低验证损失: {min_val_loss:.6f} (在第 {epoch_at_min} 轮)\n"
            f"总训练时间: {total_time_str} ({total_time_sec:.2f} 秒)\n"
            f"可训练参数: {params:,}\n"
            f"使用设备: {device}"
        )
        logger.info(f"Training Results:\n{result_message}")
        QMessageBox.information(self, "训练完成", result_message)  # 恢复

        self.progress_bar.setValue(self.progress_bar.maximum())  # 恢复
        self.progress_bar.setFormat("训练完成")  # 恢复
        QTimer.singleShot(5000, lambda: self.progress_bar.setVisible(False))  # 恢复

        # --- Restore DB Operations ---
        if (self.model is not None and self.data is not None and not self.data.empty and
                self.scalers is not None and self.processed_cols is not None and self.feature_cols is not None):

            model_type = self.model.original_model_type
            epochs_requested = self.epoch_selector.value()
            seq_length = self.seq_length
            input_dim = len(self.feature_cols)

            logger.info("Attempting to save training results to database...")  # 恢复日志
            self.update_status_bar("正在保存结果到数据库...", 0)  # 恢复状态栏
            try:
                # 1. Insert model base info (恢复)
                logger.info("   Inserting model info...")
                self.current_model_id = self.db_manager.insert_model(
                    model_type=model_type,
                    epochs_requested=epochs_requested,
                    epochs_run=epochs_run,
                    seq_length=seq_length,
                    config=self.current_model_config,
                    final_val_loss=final_val_loss,
                    input_dim=input_dim
                )

                if self.current_model_id is not None:
                    logger.info(f"   Model base info saved with ID: {self.current_model_id}")
                    # 2. Insert training logs batch (恢复)
                    logger.info("   Inserting training logs...")
                    self.db_manager.insert_training_logs_batch(self.current_model_id, log_history)
                    logger.info(
                        f"   Training logs possibly saved for model ID {self.current_model_id}.")  # 修改措辞，因为我们不知道它是否成功

                    # 3. Insert training summary (恢复)
                    logger.info("   Inserting training summary...")
                    summary_inserted = self.db_manager.insert_training_summary(
                        model_id=self.current_model_id, min_val_loss=min_val_loss,
                        epoch_at_min=epoch_at_min, final_train_loss=final_train_loss,
                        total_time_sec=total_time_sec, params=params, device=device
                    )
                    if summary_inserted:
                        logger.info(f"   Training summary saved for model ID {self.current_model_id}.")
                    else:
                        logger.warning(f"   Failed to save training summary for model ID {self.current_model_id}.")

                    # 4. Refresh model comparison table (恢复这一行)
                    logger.info("   Refreshing model comparison table...")
                    self.load_model_comparison_data()

                    # 保存模型文件到saved_models目录
                    try:
                        # 确保saved_models目录存在
                        model_save_dir = "saved_models"
                        if not os.path.exists(model_save_dir):
                            os.makedirs(model_save_dir)
                            logger.info(f"   创建模型保存目录: {model_save_dir}")

                        # 保存模型文件
                        model_file_path = os.path.join(model_save_dir, f"model_{self.current_model_id}.pth")
                        self.model.save_model(model_file_path)
                        logger.info(f"   模型已保存到文件: {model_file_path}")
                    except Exception as save_err:
                        logger.error(f"   保存模型文件时出错: {save_err}", exc_info=True)
                        QMessageBox.warning(self, "保存警告", f"无法将模型保存到文件:\n{save_err}\n\n模型信息已保存到数据库，但模型文件未保存。")

                    # 更新模型目标标签
                    self.model_target_label.setText(f"当前模型: 预测 {self.target_col}")
                    self.model_target_label.setStyleSheet("font-weight: bold; color: #008800;")

                    logger.info("   Displaying single-step prediction with history after training...")
                    self.show_prediction_result()
                    self.tab_widget.setCurrentWidget(self.tab_data_viz)

                    self.update_status_bar("训练完成，结果已保存到数据库和模型文件。", 5000)  # 更新提示

                else:
                    logger.error("   Failed to insert model base info into database. Logs and summary not saved.")
                    QMessageBox.warning(self, "数据库错误", "无法将模型基础信息保存到数据库，训练日志和摘要也未保存。")

            except Exception as db_err:
                logger.error(f"   Error during database operations: {db_err}", exc_info=True)
                QMessageBox.critical(self, "数据库错误", f"保存训练结果到数据库时出错:\n{db_err}")
                self.update_status_bar("错误：保存结果到数据库失败。", 10000)

        else:
            logger.warning("Model, data, or scaler missing after training. Results not saved to DB.")
            QMessageBox.warning(self, "状态错误", "训练完成，但应用程序状态丢失，无法保存结果。")

        # 控件启用移到 on_thread_finished
        # self.set_controls_enabled(True)
        # self.btn_stop_train.setEnabled(False); self.btn_stop_train.setText("停止训练")

        logger.info("on_training_complete finished (DB Restore step).")


    def on_training_error(self, error_message):
        """处理训练线程中的错误。"""
        logger.error(f"收到训练错误信号: {error_message}")
        self.is_training = False # 更新状态

        QMessageBox.critical(self, "训练错误", f"训练过程中发生严重错误:\n{error_message}")

        # 重置UI状态
        self.progress_bar.setValue(0)
        self.progress_bar.setFormat("训练失败")
        # 保持进度条短暂可见以显示错误状态
        QTimer.singleShot(8000, lambda: self.progress_bar.setVisible(False))
        self.set_controls_enabled(True) # 重新启用控件
        self.btn_stop_train.setEnabled(False); self.btn_stop_train.setText("停止训练")

        # Clear potentially invalid model state
        self.model = None
        self.current_model_id = None
        self.current_model_config = {}
        self.btn_predict_single.setEnabled(False)
        self.btn_predict_24h.setEnabled(False)
        self.btn_evaluate_loaded.setEnabled(False)

        self.update_status_bar("错误：训练失败。", 10000)

    def on_thread_finished(self):
        """处理训练线程结束的信号，无论是正常结束还是被用户中断。"""
        logger.info("!!! THREAD FINISHED SIGNAL RECEIVED !!!")
        self.is_training = False  # 重置训练标志

        # 检查是否是用户主动停止的训练
        was_stopped_by_user = False
        if hasattr(self, 'train_thread') and self.train_thread is not None:
            was_stopped_by_user = not self.train_thread._is_running

        # 绘图清理代码
        logger.info("尝试清理训练图表...")
        try:
            # 清除图表
            self.loss_ax.clear()
            if self.lr_ax:  # 检查lr_ax是否存在
                self.lr_ax.clear()
                self.lr_ax.get_yaxis().set_visible(False)  # 隐藏Y轴

            # 重新初始化绘图元素，以便下一次训练
            if was_stopped_by_user:
                self.loss_ax.set_title('训练过程监控 (已停止)')  # 设置标题
            else:
                self.loss_ax.set_title('训练过程监控')  # 设置标题
            self.loss_ax.set_xlabel('训练轮次')
            self.loss_ax.set_ylabel('损失值')
            self.loss_ax.grid(True)  # 重新启用网格

            # 确保lr_ax存在
            if not hasattr(self, 'lr_ax') or self.lr_ax is None:
                self.lr_ax = self.loss_ax.twinx()

            # 创建空的线对象
            self.train_loss_line, = self.loss_ax.plot([], [], 'b-', marker='.', markersize=3, label='训练损失')
            self.val_loss_line, = self.loss_ax.plot([], [], 'r-', marker='.', markersize=3, label='验证损失')
            self.lr_line, = self.lr_ax.plot([], [], 'g:', markersize=3, label='学习率')
            self.lr_ax.set_ylabel('学习率', color='g', fontsize=9)
            self.lr_ax.tick_params(axis='y', labelcolor='g', labelsize=8)
            self.lr_ax.get_yaxis().set_visible(False)  # 初始隐藏，直到有LR数据

            self.update_training_plot_legend()  # 更新图例
            self.train_canvas.draw_idle()  # 使用draw_idle刷新
            logger.info("训练图表已成功清理并重绘。")
        except Exception as e:
            logger.error(f"在on_thread_finished中清理/重绘训练图表时出错: {e}", exc_info=True)  # 记录详细错误

        # 重新启用控件
        logger.info("从on_thread_finished重新启用控件。")
        self.set_controls_enabled(True)  # 为安全起见移到这里
        self.btn_stop_train.setEnabled(False)
        self.btn_stop_train.setText("停止训练")

        # 如果是用户停止的训练，显示消息
        if was_stopped_by_user:
            QMessageBox.information(self, "训练已停止", "训练已按用户请求停止。")
            self.update_status_bar("训练已被用户停止。", 5000)
        else:
            # 可选地在短暂延迟后隐藏进度条
            QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))
            self.update_status_bar("训练线程已结束。", 5000)

        logger.info("!!! THREAD FINISHED HANDLER COMPLETED !!!")


    def update_status_bar_from_thread(self, message):
        """槽函数，用于从训练线程更新状态栏消息"""
        # Don't set timeout for messages from thread, let next message replace it
        self.update_status_bar(message, 0)

    def show_prediction_result(self, _triggered_by_signal=None, show_historical=True, num_historical_steps=1000):
        print(f"DEBUG: show_prediction_result CALLED. show_historical IS INITIALLY {show_historical}")
        """
        显示单步预测结果可视化，清除原图后绘制历史数据和预测值。
        Args:
            show_historical (bool): 是否在图中包含历史实际数据。
            num_historical_steps (int): 要显示的历史数据点数量。
        """
        try:
            print(f"DEBUG: Name of self.show_prediction_result: {self.show_prediction_result.__name__}")
            print(f"DEBUG: Module of self.show_prediction_result: {self.show_prediction_result.__module__}")
        except AttributeError:
            print("DEBUG: self.show_prediction_result might not be a standard method (e.g., lambda)")
        # === 打印调用栈信息 ===
        stack = inspect.stack()
        caller_frame = stack[1]  # 0 是当前函数，1 是调用者
        caller_func = caller_frame.function
        caller_lineno = caller_frame.lineno
        logger.debug(
            f"show_prediction_result 被函数 '{caller_func}' (行号 {caller_lineno}) 调用，参数 show_historical={show_historical}")
        logger.info("尝试显示单步预测（清除之前的图形）...")

        # --- 预检查 (更新以支持新的scalers结构) ---
        if self.model is None:
            QMessageBox.warning(self, "无模型", "没有活动的模型可用于预测。请先训练或加载模型。")
            return
        if self.data is None or self.data.empty:
            QMessageBox.warning(self, "无数据", "缺少数据，无法进行预测。")
            return

        # 检查是否有特征列
        if not self.feature_cols:
            QMessageBox.warning(self, "数据状态不完整", "缺少进行预测所需的特征列信息。")
            logger.warning("预测预检查失败：缺少特征列信息。")
            return

        # 检查归一化器 - 支持新旧两种结构
        has_scaler = False
        if hasattr(self, 'scalers') and self.scalers is not None:
            # 新结构 - 使用scalers字典
            if 'minmax_scaler' in self.scalers and self.scalers['minmax_scaler'] is not None:
                has_scaler = True
            elif 'robust_scaler' in self.scalers and self.scalers['robust_scaler'] is not None:
                has_scaler = True
        elif hasattr(self, 'scaler') and self.scaler is not None:
            # 旧结构 - 使用单一scaler
            has_scaler = True

        if not has_scaler:
            QMessageBox.warning(self, "数据状态不完整", "缺少进行预测所需的归一化器。")
            logger.warning("预测预检查失败：缺少归一化器。")
            return

        # 检查列信息
        has_column_info = False
        if hasattr(self, 'minmax_cols') and self.minmax_cols:
            has_column_info = True
        elif hasattr(self, 'robust_cols') and self.robust_cols:
            has_column_info = True
        elif hasattr(self, 'numeric_cols') and self.numeric_cols:
            has_column_info = True

        if not has_column_info:
            QMessageBox.warning(self, "数据状态不完整", "缺少进行预测所需的列信息。")
            logger.warning("预测预检查失败：缺少列信息。")
            return

        # 检查目标列索引和最后一行数据
        if not hasattr(self, 'target_col_index_in_numeric') or self.target_col_index_in_numeric is None:
            QMessageBox.warning(self, "数据状态不完整", "缺少进行预测所需的目标列索引信息。")
            logger.warning("预测预检查失败：缺少目标列索引信息。")
            return

        if not hasattr(self, 'last_scaled_numeric_row') or self.last_scaled_numeric_row is None:
            QMessageBox.warning(self, "数据状态不完整", "缺少进行预测所需的最后一行数据。")
            logger.warning("预测预检查失败：缺少最后一行数据。")
            return

        seq_length = self.seq_length
        if len(self.data) < seq_length:
            QMessageBox.warning(self, "数据不足",
                                f"当前数据点 ({len(self.data)}) 少于所需序列长度 ({seq_length})，无法进行预测。")
            return

        self.update_status_bar("正在进行单步预测...", 0)
        QApplication.processEvents()

        try:
            target_col = self.target_col
            temp_index_in_numeric = self.target_col_index_in_numeric

            # --- 准备输入序列 (增加详细日志) ---
            last_sequence_features_scaled = self.data[self.feature_cols].values[-seq_length:]
            input_tensor_np = last_sequence_features_scaled[np.newaxis, :, :].astype(np.float32)
            logger.debug(f"预测输入形状: {input_tensor_np.shape}")

            # 记录输入张量的统计信息
            input_mean = np.mean(input_tensor_np)
            input_std = np.std(input_tensor_np)
            input_min = np.min(input_tensor_np)
            input_max = np.max(input_tensor_np)
            logger.debug(f"输入张量统计: 均值={input_mean:.6f}, 标准差={input_std:.6f}, 最小值={input_min:.6f}, 最大值={input_max:.6f}")

            # 记录目标列在输入序列中的值（如果目标列也是特征之一）
            if target_col in self.feature_cols:
                target_idx = self.feature_cols.index(target_col)
                target_values = input_tensor_np[0, -5:, target_idx]  # 获取最后5个时间步的目标值
                logger.debug(f"输入序列中的目标列 ({target_col}) 最后5个值: {target_values}")

                # 计算目标列的统计信息
                target_mean = np.mean(input_tensor_np[0, :, target_idx])
                target_std = np.std(input_tensor_np[0, :, target_idx])
                logger.debug(f"输入序列中目标列 ({target_col}) 统计: 均值={target_mean:.6f}, 标准差={target_std:.6f}")

            # 记录特征列的前几个值
            for i, feature in enumerate(self.feature_cols[:5]):  # 只记录前5个特征
                feature_values = input_tensor_np[0, -1, i]  # 最后一个时间步的特征值
                logger.debug(f"特征 '{feature}' 最后一个时间步的值: {feature_values:.6f}")

            # --- 执行预测 (增加详细日志) ---
            device = self.get_selected_device()
            logger.debug(f"使用设备: {device} 进行预测")

            # 确保模型处于评估模式
            self.model.eval()
            logger.debug("模型已设置为评估模式")

            prediction_array = self.model.predict(input_tensor_np, device=device)
            predicted_value_scaled = float(prediction_array.item(0))
            logger.info(f"单步预测（缩放后）: {predicted_value_scaled:.6f}")

            # 获取最后一个实际值（如果可用）
            if target_col in self.data.columns:
                last_actual_scaled = self.data[target_col].iloc[-1]
                logger.debug(f"最后一个实际值（缩放后）: {last_actual_scaled:.6f}")

                # 计算差异
                diff = predicted_value_scaled - last_actual_scaled
                diff_percent = (diff / last_actual_scaled) * 100 if last_actual_scaled != 0 else float('inf')
                logger.debug(f"预测值与最后实际值的差异: {diff:.6f} ({diff_percent:.2f}%)")
            if np.isnan(predicted_value_scaled):
                raise ValueError("模型预测返回 NaN。")

            # --- 反向变换 (更新以支持新的scalers结构) ---
            # 确定目标列在哪个归一化组中，并使用相应的scaler
            if hasattr(self, 'minmax_cols') and target_col in self.minmax_cols and 'minmax_scaler' in self.scalers:
                # 使用minmax_scaler进行反向变换
                logger.debug(f"目标列 {target_col} 在minmax_cols中，使用minmax_scaler进行反向变换")

                try:
                    # 获取scaler训练时使用的特征列
                    scaler_to_use = self.scalers['minmax_scaler']

                    # 检查scaler的形状
                    scaler_n_features = scaler_to_use.scale_.shape[0]
                    logger.debug(f"minmax_scaler期望的特征数量: {scaler_n_features}")
                    logger.debug(f"当前minmax_cols的长度: {len(self.minmax_cols)}")

                    # 如果scaler的特征数量与minmax_cols不匹配，使用更简单的方法
                    if scaler_n_features != len(self.minmax_cols):
                        logger.warning(f"scaler特征数量 ({scaler_n_features}) 与minmax_cols长度 ({len(self.minmax_cols)}) 不匹配")
                        logger.warning("使用简化的反向变换方法")

                        # 使用简化的方法：创建一个只包含一个特征的数组
                        dummy_array = np.array([[predicted_value_scaled]])

                        # 计算反向变换
                        # 假设MinMaxScaler使用公式: X_std * (max - min) + min
                        # 其中X_std是缩放后的值 (0-1之间)
                        feature_min = scaler_to_use.min_[0]  # 使用第一个特征的最小值
                        feature_max = feature_min + scaler_to_use.scale_[0]  # min + scale = max

                        # 手动计算反向变换
                        prediction_unscaled = predicted_value_scaled * (feature_max - feature_min) + feature_min
                        logger.info(f"使用简化方法计算的未缩放预测值: {prediction_unscaled:.4f}")
                    else:
                        # 正常方法：使用scaler的inverse_transform
                        cols_to_use = self.minmax_cols[:scaler_n_features]  # 确保长度匹配
                        col_index = cols_to_use.index(target_col) if target_col in cols_to_use else 0

                        # 创建一个包含所有minmax特征缩放值的DataFrame
                        df_to_inverse_transform = pd.DataFrame(np.zeros((1, len(cols_to_use))), columns=cols_to_use)
                        # 更新DataFrame中目标列的缩放值为预测得到的值
                        if target_col in cols_to_use:
                            df_to_inverse_transform.loc[0, target_col] = predicted_value_scaled

                        # 执行反向变换
                        inverse_transformed_array = scaler_to_use.inverse_transform(df_to_inverse_transform)
                        # 从反向变换后的数组中提取目标列的未缩放值
                        prediction_unscaled = inverse_transformed_array[0, col_index]
                        logger.info(f"使用scaler.inverse_transform计算的未缩放预测值: {prediction_unscaled:.4f}")

                except Exception as e:
                    logger.error(f"反向变换时出错: {e}", exc_info=True)
                    # 使用最简单的方法：假设缩放值在0-1之间，线性映射到一个合理范围
                    # 对于股票数据，假设范围是0-1000
                    if CURRENT_DATA_TYPE == 'STOCK' and target_col == 'volume':
                        prediction_unscaled = predicted_value_scaled * 1000000  # 假设成交量在0-1000000之间
                    else:
                        prediction_unscaled = predicted_value_scaled * 100  # 默认映射到0-100
                    logger.warning(f"使用应急方法计算的未缩放预测值: {prediction_unscaled:.4f}")

            elif hasattr(self, 'robust_cols') and target_col in self.robust_cols and 'robust_scaler' in self.scalers:
                # 使用robust_scaler进行反向变换
                logger.debug(f"目标列 {target_col} 在robust_cols中，使用robust_scaler进行反向变换")

                try:
                    # 获取scaler训练时使用的特征列
                    scaler_to_use = self.scalers['robust_scaler']

                    # 检查scaler的形状
                    scaler_n_features = scaler_to_use.center_.shape[0]
                    logger.debug(f"robust_scaler期望的特征数量: {scaler_n_features}")
                    logger.debug(f"当前robust_cols的长度: {len(self.robust_cols)}")

                    # 如果scaler的特征数量与robust_cols不匹配，使用更简单的方法
                    if scaler_n_features != len(self.robust_cols):
                        logger.warning(f"scaler特征数量 ({scaler_n_features}) 与robust_cols长度 ({len(self.robust_cols)}) 不匹配")
                        logger.warning("使用简化的反向变换方法")

                        # 使用简化的方法：手动计算反向变换
                        # RobustScaler使用公式: X_scaled * scale_ + center_
                        center = scaler_to_use.center_[0]  # 使用第一个特征的中心值
                        scale = scaler_to_use.scale_[0]    # 使用第一个特征的缩放因子

                        # 手动计算反向变换
                        prediction_unscaled = predicted_value_scaled * scale + center
                        logger.info(f"使用简化方法计算的未缩放预测值: {prediction_unscaled:.4f}")
                    else:
                        # 正常方法：使用scaler的inverse_transform
                        cols_to_use = self.robust_cols[:scaler_n_features]  # 确保长度匹配
                        col_index = cols_to_use.index(target_col) if target_col in cols_to_use else 0

                        # 创建一个包含所有robust特征缩放值的DataFrame
                        df_to_inverse_transform = pd.DataFrame(np.zeros((1, len(cols_to_use))), columns=cols_to_use)
                        # 更新DataFrame中目标列的缩放值为预测得到的值
                        if target_col in cols_to_use:
                            df_to_inverse_transform.loc[0, target_col] = predicted_value_scaled

                        # 执行反向变换
                        inverse_transformed_array = scaler_to_use.inverse_transform(df_to_inverse_transform)
                        # 从反向变换后的数组中提取目标列的未缩放值
                        prediction_unscaled = inverse_transformed_array[0, col_index]
                        logger.info(f"使用scaler.inverse_transform计算的未缩放预测值: {prediction_unscaled:.4f}")

                except Exception as e:
                    logger.error(f"反向变换时出错: {e}", exc_info=True)
                    # 使用最简单的方法：假设缩放值在-5到5之间，线性映射到一个合理范围
                    if CURRENT_DATA_TYPE == 'STOCK' and target_col == 'price_change':
                        prediction_unscaled = predicted_value_scaled * 10  # 假设价格变化在-50到50之间
                    else:
                        prediction_unscaled = predicted_value_scaled * 20  # 默认映射到-100到100
                    logger.warning(f"使用应急方法计算的未缩放预测值: {prediction_unscaled:.4f}")

            elif hasattr(self, 'numeric_cols') and target_col in self.numeric_cols and hasattr(self, 'scaler') and self.scaler is not None:
                # 兼容旧版本 - 使用单一scaler
                logger.debug(f"目标列 {target_col} 在numeric_cols中，使用旧版本scaler进行反向变换")

                try:
                    # 检查scaler和last_scaled_numeric_row是否可用
                    if self.scaler is None:
                        raise ValueError("scaler对象为None")

                    if self.last_scaled_numeric_row is None:
                        raise ValueError("last_scaled_numeric_row为None")

                    # 检查形状是否匹配
                    expected_shape = len(self.numeric_cols)
                    actual_shape = len(self.last_scaled_numeric_row)

                    logger.debug(f"scaler期望的特征数量: {expected_shape}")
                    logger.debug(f"last_scaled_numeric_row的长度: {actual_shape}")

                    if expected_shape != actual_shape:
                        logger.warning(f"numeric_cols长度 ({expected_shape}) 与last_scaled_numeric_row长度 ({actual_shape}) 不匹配")
                        # 创建一个新的数组，长度与last_scaled_numeric_row一致
                        dummy_row = np.zeros(actual_shape)
                        # 如果目标列索引有效，设置预测值
                        if temp_index_in_numeric is not None and temp_index_in_numeric < actual_shape:
                            dummy_row[temp_index_in_numeric] = predicted_value_scaled
                        else:
                            # 否则使用第一个位置
                            dummy_row[0] = predicted_value_scaled

                        # 执行反向变换
                        inverse_transformed_array = self.scaler.inverse_transform(dummy_row.reshape(1, -1))
                        # 从反向变换后的数组中提取目标列的未缩放值
                        prediction_unscaled = inverse_transformed_array[0, 0 if temp_index_in_numeric is None else temp_index_in_numeric]
                    else:
                        # 创建一个包含所有数值特征缩放值的DataFrame，列名与scaler训练时一致
                        df_to_inverse_transform = pd.DataFrame(self.last_scaled_numeric_row.copy().reshape(1, -1),
                                                              columns=self.numeric_cols)
                        # 更新DataFrame中目标列的缩放值为预测得到的值
                        df_to_inverse_transform.loc[0, target_col] = predicted_value_scaled

                        # 执行反向变换
                        inverse_transformed_array = self.scaler.inverse_transform(df_to_inverse_transform)
                        # 从反向变换后的数组中提取目标列的未缩放值
                        prediction_unscaled = inverse_transformed_array[0, temp_index_in_numeric]

                    logger.info(f"使用旧版本scaler计算的未缩放预测值: {prediction_unscaled:.4f}")

                except Exception as e:
                    logger.error(f"使用旧版本scaler进行反向变换时出错: {e}", exc_info=True)
                    # 使用最简单的方法：根据数据类型和目标列进行线性映射
                    if CURRENT_DATA_TYPE == 'STOCK':
                        if target_col == 'volume':
                            prediction_unscaled = predicted_value_scaled * 1000000  # 假设成交量在0-1000000之间
                        elif target_col in ['open_price', 'close_price', 'high_price', 'low_price']:
                            prediction_unscaled = predicted_value_scaled * 100  # 假设价格在0-100之间
                        else:
                            prediction_unscaled = predicted_value_scaled * 50  # 默认映射到0-50
                    elif CURRENT_DATA_TYPE == 'WEATHER':
                        if target_col == 'temperature':
                            prediction_unscaled = predicted_value_scaled * 40 - 10  # 假设温度在-10到30之间
                        elif target_col == 'humidity':
                            prediction_unscaled = predicted_value_scaled * 100  # 假设湿度在0-100之间
                        elif target_col == 'pressure':
                            prediction_unscaled = predicted_value_scaled * 300 + 800  # 假设气压在800-1100之间
                        else:
                            prediction_unscaled = predicted_value_scaled * 50  # 默认映射到0-50
                    else:
                        prediction_unscaled = predicted_value_scaled * 100  # 默认映射到0-100

                    logger.warning(f"使用应急方法计算的未缩放预测值: {prediction_unscaled:.4f}")

            else:
                # 如果找不到合适的scaler，抛出错误
                raise ValueError(f"无法找到目标列 {target_col} 对应的归一化器。请检查数据处理流程。")

            logger.info(f"单步预测（未缩放）: {prediction_unscaled:.4f} ({target_col})")

            # 确定预测时间戳
            actual_timestamps = self.data.index
            if len(actual_timestamps) >= 2:
                time_interval = actual_timestamps[-1] - actual_timestamps[-2]
                if time_interval.total_seconds() <= 0:
                    logger.warning(f"计算的时间间隔为非正值 ({time_interval})，使用默认10分钟。")
                    time_interval = pd.Timedelta(minutes=10)
            else:
                logger.warning("时间戳不足以确定间隔，使用默认10分钟。")
                time_interval = pd.Timedelta(minutes=10)
            prediction_timestamp = actual_timestamps[-1] + time_interval
            logger.info(f"预测目标时间戳: {prediction_timestamp}")

            # 更新绘图逻辑：清除并重绘
            logger.info("清除数据图形并重新绘制历史 + 单步预测...")
            self.data_fig.clf()  # 清除整个 Figure
            ax = self.data_fig.add_subplot(111) # 只添加这一个 Axes

            original_target_name = next((k for k, v in COLUMN_MAPPING.items() if v == target_col), target_col)
            plot_title = f'单步预测 (ID: {self.current_model_id or "N/A"})'

            # --- 绘制历史数据 (更新以支持新的scalers结构) ---
            logger.debug(f"进入历史数据绘制判断前: show_historical = {show_historical}")
            if show_historical:
                plot_range = min(len(self.data), num_historical_steps)
                if plot_range > 0:
                    logger.debug("已进入 plot_range > 0 分支...")
                    actual_plot_timestamps = actual_timestamps[-plot_range:]

                    # 确定目标列在哪个归一化组中
                    if hasattr(self, 'minmax_cols') and target_col in self.minmax_cols and 'minmax_scaler' in self.scalers:
                        # 使用minmax_scaler进行反向变换
                        logger.debug(f"历史数据：目标列 {target_col} 在minmax_cols中，使用minmax_scaler")

                        try:
                            # 获取scaler训练时使用的特征列
                            scaler_to_use = self.scalers['minmax_scaler']

                            # 检查scaler的形状
                            scaler_n_features = scaler_to_use.scale_.shape[0]
                            logger.debug(f"minmax_scaler期望的特征数量: {scaler_n_features}")
                            logger.debug(f"当前minmax_cols的长度: {len(self.minmax_cols)}")

                            # 如果scaler的特征数量与minmax_cols不匹配，使用简化的方法
                            if scaler_n_features != len(self.minmax_cols):
                                logger.warning(f"历史数据：scaler特征数量 ({scaler_n_features}) 与minmax_cols长度 ({len(self.minmax_cols)}) 不匹配")
                                logger.warning("历史数据：使用简化的反向变换方法")

                                # 获取目标列的缩放数据
                                target_scaled_data = self.data[target_col].iloc[-plot_range:].values
                                logger.debug(f"历史数据 (目标列缩放后) shape: {target_scaled_data.shape}")

                                # 手动计算反向变换
                                # 假设MinMaxScaler使用公式: X_std * (max - min) + min
                                feature_min = scaler_to_use.min_[0]  # 使用第一个特征的最小值
                                feature_max = feature_min + scaler_to_use.scale_[0]  # min + scale = max

                                # 应用到所有历史数据点
                                actual_plot_target_unscaled = target_scaled_data * (feature_max - feature_min) + feature_min
                                logger.debug(f"历史数据 (手动反向变换后) shape: {actual_plot_target_unscaled.shape}")

                                # 检查NaN/Inf
                                nan_count = np.isnan(actual_plot_target_unscaled).sum()
                                inf_count = np.isinf(actual_plot_target_unscaled).sum()
                                logger.debug(f"检查未缩放历史目标值: NaN数量={nan_count}, Inf数量={inf_count}")

                                valid_indices = ~np.isnan(actual_plot_target_unscaled) & ~np.isinf(actual_plot_target_unscaled)
                                num_valid_hist = np.sum(valid_indices)
                                logger.debug(f"有效历史点数量: {num_valid_hist}")

                                if num_valid_hist > 0:
                                    logger.info(f"准备绘制 {num_valid_hist} 个有效的历史点...")
                                    logger.debug("调用 ax.plot 绘制历史数据...")
                                    ax.plot(actual_plot_timestamps[valid_indices], actual_plot_target_unscaled[valid_indices],
                                            'b.-', markersize=5, linewidth=1, label=f'实际值 (最近 {plot_range} 步)')
                                    logger.debug("ax.plot 绘制历史数据调用完成。")
                                    plot_title = f'最近 {num_historical_steps} 步实际值与单步预测 (ID: {self.current_model_id or "N/A"})'
                                else:
                                    logger.warning("没有有效的历史数据点可供绘制。")
                                    ax.text(0.5, 0.6, "无有效历史数据", color='orange', ha='center', transform=ax.transAxes)
                            else:
                                # 正常方法：使用scaler的inverse_transform
                                cols_to_use = self.minmax_cols[:scaler_n_features]  # 确保长度匹配
                                col_index = cols_to_use.index(target_col) if target_col in cols_to_use else 0

                                # 获取最近的缩放数据
                                recent_scaled_data = self.data[cols_to_use].iloc[-plot_range:].values
                                logger.debug(f"历史数据 (minmax缩放后) shape: {recent_scaled_data.shape}")

                                # 反向变换
                                unscaled_actual_data = scaler_to_use.inverse_transform(recent_scaled_data)
                                # 提取目标列
                                actual_plot_target_unscaled = unscaled_actual_data[:, col_index]
                                logger.debug(f"历史数据 (minmax未缩放目标) shape: {actual_plot_target_unscaled.shape}")

                                # 检查NaN/Inf
                                nan_count = np.isnan(actual_plot_target_unscaled).sum()
                                inf_count = np.isinf(actual_plot_target_unscaled).sum()
                                logger.debug(f"检查未缩放历史目标值: NaN数量={nan_count}, Inf数量={inf_count}")

                                valid_indices = ~np.isnan(actual_plot_target_unscaled) & ~np.isinf(actual_plot_target_unscaled)
                                num_valid_hist = np.sum(valid_indices)
                                logger.debug(f"有效历史点数量: {num_valid_hist}")

                                if num_valid_hist > 0:
                                    logger.info(f"准备绘制 {num_valid_hist} 个有效的历史点...")
                                    logger.debug("调用 ax.plot 绘制历史数据...")
                                    ax.plot(actual_plot_timestamps[valid_indices], actual_plot_target_unscaled[valid_indices],
                                            'b.-', markersize=5, linewidth=1, label=f'实际值 (最近 {plot_range} 步)')
                                    logger.debug("ax.plot 绘制历史数据调用完成。")
                                    plot_title = f'最近 {num_historical_steps} 步实际值与单步预测 (ID: {self.current_model_id or "N/A"})'
                                else:
                                    logger.warning("没有有效的历史数据点可供绘制。")
                                    ax.text(0.5, 0.6, "无有效历史数据", color='orange', ha='center', transform=ax.transAxes)
                        except Exception as e:
                            logger.error(f"历史数据：反向变换时出错: {e}", exc_info=True)
                            # 使用简单的线性映射作为回退
                            target_scaled_data = self.data[target_col].iloc[-plot_range:].values

                            # 根据数据类型和目标列选择合适的映射
                            if CURRENT_DATA_TYPE == 'STOCK' and target_col == 'volume':
                                actual_plot_target_unscaled = target_scaled_data * 1000000  # 假设成交量在0-1000000之间
                            elif CURRENT_DATA_TYPE == 'STOCK':
                                actual_plot_target_unscaled = target_scaled_data * 100  # 假设价格在0-100之间
                            else:
                                actual_plot_target_unscaled = target_scaled_data * 50  # 默认映射到0-50

                            logger.warning(f"历史数据：使用应急方法计算的未缩放值")

                            # 检查NaN/Inf
                            nan_count = np.isnan(actual_plot_target_unscaled).sum()
                            inf_count = np.isinf(actual_plot_target_unscaled).sum()
                            logger.debug(f"检查未缩放历史目标值: NaN数量={nan_count}, Inf数量={inf_count}")

                            valid_indices = ~np.isnan(actual_plot_target_unscaled) & ~np.isinf(actual_plot_target_unscaled)
                            num_valid_hist = np.sum(valid_indices)
                            logger.debug(f"有效历史点数量: {num_valid_hist}")

                            if num_valid_hist > 0:
                                logger.info(f"准备绘制 {num_valid_hist} 个有效的历史点...")
                                logger.debug("调用 ax.plot 绘制历史数据...")
                                ax.plot(actual_plot_timestamps[valid_indices], actual_plot_target_unscaled[valid_indices],
                                        'b.-', markersize=5, linewidth=1, label=f'实际值 (最近 {plot_range} 步)')
                                logger.debug("ax.plot 绘制历史数据调用完成。")
                                plot_title = f'最近 {num_historical_steps} 步实际值与单步预测 (ID: {self.current_model_id or "N/A"})'
                            else:
                                logger.warning("没有有效的历史数据点可供绘制。")
                                ax.text(0.5, 0.6, "无有效历史数据", color='orange', ha='center', transform=ax.transAxes)

                        except Exception as e:
                            logger.error(f"处理或绘制minmax历史数据时发生错误: {e}", exc_info=True)
                            ax.text(0.5, 0.6, "无法绘制历史数据", color='orange', ha='center', transform=ax.transAxes)

                    elif hasattr(self, 'robust_cols') and target_col in self.robust_cols and 'robust_scaler' in self.scalers:
                        # 使用robust_scaler进行反向变换
                        logger.debug(f"历史数据：目标列 {target_col} 在robust_cols中，使用robust_scaler")

                        try:
                            # 获取scaler训练时使用的特征列
                            scaler_to_use = self.scalers['robust_scaler']

                            # 检查scaler的形状
                            scaler_n_features = scaler_to_use.center_.shape[0]
                            logger.debug(f"robust_scaler期望的特征数量: {scaler_n_features}")
                            logger.debug(f"当前robust_cols的长度: {len(self.robust_cols)}")

                            # 如果scaler的特征数量与robust_cols不匹配，使用简化的方法
                            if scaler_n_features != len(self.robust_cols):
                                logger.warning(f"历史数据：scaler特征数量 ({scaler_n_features}) 与robust_cols长度 ({len(self.robust_cols)}) 不匹配")
                                logger.warning("历史数据：使用简化的反向变换方法")

                                # 获取目标列的缩放数据
                                target_scaled_data = self.data[target_col].iloc[-plot_range:].values
                                logger.debug(f"历史数据 (目标列缩放后) shape: {target_scaled_data.shape}")

                                # 手动计算反向变换
                                # RobustScaler使用公式: X_scaled * scale_ + center_
                                center = scaler_to_use.center_[0]  # 使用第一个特征的中心值
                                scale = scaler_to_use.scale_[0]    # 使用第一个特征的缩放因子

                                # 应用到所有历史数据点
                                actual_plot_target_unscaled = target_scaled_data * scale + center
                                logger.debug(f"历史数据 (手动反向变换后) shape: {actual_plot_target_unscaled.shape}")

                                # 检查NaN/Inf
                                nan_count = np.isnan(actual_plot_target_unscaled).sum()
                                inf_count = np.isinf(actual_plot_target_unscaled).sum()
                                logger.debug(f"检查未缩放历史目标值: NaN数量={nan_count}, Inf数量={inf_count}")

                                valid_indices = ~np.isnan(actual_plot_target_unscaled) & ~np.isinf(actual_plot_target_unscaled)
                                num_valid_hist = np.sum(valid_indices)
                                logger.debug(f"有效历史点数量: {num_valid_hist}")

                                if num_valid_hist > 0:
                                    logger.info(f"准备绘制 {num_valid_hist} 个有效的历史点...")
                                    logger.debug("调用 ax.plot 绘制历史数据...")
                                    ax.plot(actual_plot_timestamps[valid_indices], actual_plot_target_unscaled[valid_indices],
                                            'b.-', markersize=5, linewidth=1, label=f'实际值 (最近 {plot_range} 步)')
                                    logger.debug("ax.plot 绘制历史数据调用完成。")
                                    plot_title = f'最近 {num_historical_steps} 步实际值与单步预测 (ID: {self.current_model_id or "N/A"})'
                                else:
                                    logger.warning("没有有效的历史数据点可供绘制。")
                                    ax.text(0.5, 0.6, "无有效历史数据", color='orange', ha='center', transform=ax.transAxes)
                            else:
                                # 正常方法：使用scaler的inverse_transform
                                cols_to_use = self.robust_cols[:scaler_n_features]  # 确保长度匹配
                                col_index = cols_to_use.index(target_col) if target_col in cols_to_use else 0

                                # 获取最近的缩放数据
                                recent_scaled_data = self.data[cols_to_use].iloc[-plot_range:].values
                                logger.debug(f"历史数据 (robust缩放后) shape: {recent_scaled_data.shape}")

                                # 反向变换
                                unscaled_actual_data = scaler_to_use.inverse_transform(recent_scaled_data)
                                # 提取目标列
                                actual_plot_target_unscaled = unscaled_actual_data[:, col_index]
                                logger.debug(f"历史数据 (robust未缩放目标) shape: {actual_plot_target_unscaled.shape}")

                                # 检查NaN/Inf
                                nan_count = np.isnan(actual_plot_target_unscaled).sum()
                                inf_count = np.isinf(actual_plot_target_unscaled).sum()
                                logger.debug(f"检查未缩放历史目标值: NaN数量={nan_count}, Inf数量={inf_count}")

                                valid_indices = ~np.isnan(actual_plot_target_unscaled) & ~np.isinf(actual_plot_target_unscaled)
                                num_valid_hist = np.sum(valid_indices)
                                logger.debug(f"有效历史点数量: {num_valid_hist}")

                                if num_valid_hist > 0:
                                    logger.info(f"准备绘制 {num_valid_hist} 个有效的历史点...")
                                    logger.debug("调用 ax.plot 绘制历史数据...")
                                    ax.plot(actual_plot_timestamps[valid_indices], actual_plot_target_unscaled[valid_indices],
                                            'b.-', markersize=5, linewidth=1, label=f'实际值 (最近 {plot_range} 步)')
                                    logger.debug("ax.plot 绘制历史数据调用完成。")
                                    plot_title = f'最近 {num_historical_steps} 步实际值与单步预测 (ID: {self.current_model_id or "N/A"})'
                                else:
                                    logger.warning("没有有效的历史数据点可供绘制。")
                                    ax.text(0.5, 0.6, "无有效历史数据", color='orange', ha='center', transform=ax.transAxes)
                        except Exception as e:
                            logger.error(f"历史数据：反向变换时出错: {e}", exc_info=True)
                            # 使用简单的线性映射作为回退
                            target_scaled_data = self.data[target_col].iloc[-plot_range:].values

                            # 根据数据类型和目标列选择合适的映射
                            if CURRENT_DATA_TYPE == 'STOCK' and target_col == 'price_change':
                                actual_plot_target_unscaled = target_scaled_data * 10  # 假设价格变化在-50到50之间
                            else:
                                actual_plot_target_unscaled = target_scaled_data * 20  # 默认映射到-100到100

                            logger.warning(f"历史数据：使用应急方法计算的未缩放值")

                            # 检查NaN/Inf
                            nan_count = np.isnan(actual_plot_target_unscaled).sum()
                            inf_count = np.isinf(actual_plot_target_unscaled).sum()
                            logger.debug(f"检查未缩放历史目标值: NaN数量={nan_count}, Inf数量={inf_count}")

                            valid_indices = ~np.isnan(actual_plot_target_unscaled) & ~np.isinf(actual_plot_target_unscaled)
                            num_valid_hist = np.sum(valid_indices)
                            logger.debug(f"有效历史点数量: {num_valid_hist}")

                            if num_valid_hist > 0:
                                logger.info(f"准备绘制 {num_valid_hist} 个有效的历史点...")
                                logger.debug("调用 ax.plot 绘制历史数据...")
                                ax.plot(actual_plot_timestamps[valid_indices], actual_plot_target_unscaled[valid_indices],
                                        'b.-', markersize=5, linewidth=1, label=f'实际值 (最近 {plot_range} 步)')
                                logger.debug("ax.plot 绘制历史数据调用完成。")
                                plot_title = f'最近 {num_historical_steps} 步实际值与单步预测 (ID: {self.current_model_id or "N/A"})'
                            else:
                                logger.warning("没有有效的历史数据点可供绘制。")
                                ax.text(0.5, 0.6, "无有效历史数据", color='orange', ha='center', transform=ax.transAxes)

                        except Exception as e:
                            logger.error(f"处理或绘制robust历史数据时发生错误: {e}", exc_info=True)
                            ax.text(0.5, 0.6, "无法绘制历史数据", color='orange', ha='center', transform=ax.transAxes)

                    elif hasattr(self, 'numeric_cols') and target_col in self.numeric_cols and hasattr(self, 'scaler') and self.scaler is not None:
                        # 兼容旧版本 - 使用单一scaler
                        logger.debug(f"历史数据：目标列 {target_col} 在numeric_cols中，使用旧版本scaler")

                        try:
                            # 检查scaler是否可用
                            if self.scaler is None:
                                raise ValueError("scaler对象为None")

                            # 检查形状是否匹配
                            expected_shape = len(self.numeric_cols)

                            # 获取scaler的特征数量
                            if hasattr(self.scaler, 'scale_'):
                                scaler_n_features = self.scaler.scale_.shape[0]
                            elif hasattr(self.scaler, 'n_features_in_'):
                                scaler_n_features = self.scaler.n_features_in_
                            else:
                                # 如果无法确定scaler的特征数量，假设它与numeric_cols匹配
                                scaler_n_features = expected_shape

                            logger.debug(f"scaler期望的特征数量: {scaler_n_features}")
                            logger.debug(f"numeric_cols的长度: {expected_shape}")

                            if scaler_n_features != expected_shape:
                                logger.warning(f"历史数据：scaler特征数量 ({scaler_n_features}) 与numeric_cols长度 ({expected_shape}) 不匹配")
                                logger.warning("历史数据：使用简化的反向变换方法")

                                # 获取目标列的缩放数据
                                target_scaled_data = self.data[target_col].iloc[-plot_range:].values
                                logger.debug(f"历史数据 (目标列缩放后) shape: {target_scaled_data.shape}")

                                # 使用简单的线性映射作为回退
                                if CURRENT_DATA_TYPE == 'STOCK':
                                    if target_col == 'volume':
                                        actual_plot_target_unscaled = target_scaled_data * 1000000  # 假设成交量在0-1000000之间
                                    else:
                                        actual_plot_target_unscaled = target_scaled_data * 100  # 假设价格在0-100之间
                                elif CURRENT_DATA_TYPE == 'WEATHER':
                                    if target_col == 'temperature':
                                        actual_plot_target_unscaled = target_scaled_data * 40 - 10  # 假设温度在-10到30之间
                                    elif target_col == 'humidity':
                                        actual_plot_target_unscaled = target_scaled_data * 100  # 假设湿度在0-100之间
                                    elif target_col == 'pressure':
                                        actual_plot_target_unscaled = target_scaled_data * 300 + 800  # 假设气压在800-1100之间
                                    else:
                                        actual_plot_target_unscaled = target_scaled_data * 50  # 默认映射到0-50
                                else:
                                    actual_plot_target_unscaled = target_scaled_data * 100  # 默认映射到0-100

                                logger.debug(f"历史数据 (手动映射后) shape: {actual_plot_target_unscaled.shape}")

                                # 检查NaN/Inf
                                nan_count = np.isnan(actual_plot_target_unscaled).sum()
                                inf_count = np.isinf(actual_plot_target_unscaled).sum()
                                logger.debug(f"检查未缩放历史目标值: NaN数量={nan_count}, Inf数量={inf_count}")

                                valid_indices = ~np.isnan(actual_plot_target_unscaled) & ~np.isinf(actual_plot_target_unscaled)
                                num_valid_hist = np.sum(valid_indices)
                                logger.debug(f"有效历史点数量: {num_valid_hist}")

                                if num_valid_hist > 0:
                                    logger.info(f"准备绘制 {num_valid_hist} 个有效的历史点...")
                                    logger.debug("调用 ax.plot 绘制历史数据...")
                                    ax.plot(actual_plot_timestamps[valid_indices], actual_plot_target_unscaled[valid_indices],
                                            'b.-', markersize=5, linewidth=1, label=f'实际值 (最近 {plot_range} 步)')
                                    logger.debug("ax.plot 绘制历史数据调用完成。")
                                    plot_title = f'最近 {num_historical_steps} 步实际值与单步预测 (ID: {self.current_model_id or "N/A"})'
                                else:
                                    logger.warning("没有有效的历史数据点可供绘制。")
                                    ax.text(0.5, 0.6, "无有效历史数据", color='orange', ha='center', transform=ax.transAxes)
                            else:
                                # 确保只选择参与归一化的列进行反变换
                                recent_scaled_numeric = self.data[self.numeric_cols].iloc[-plot_range:].values
                                logger.debug(f"历史数据 (缩放后) shape: {recent_scaled_numeric.shape}")

                                # 执行反向变换
                                unscaled_actual_data = self.scaler.inverse_transform(recent_scaled_numeric)
                                # 确保使用正确的索引提取目标列
                                target_index = self.numeric_cols.index(target_col)
                                actual_plot_target_unscaled = unscaled_actual_data[:, target_index]
                                logger.debug(f"历史数据 (未缩放目标) shape: {actual_plot_target_unscaled.shape}")

                                # 检查NaN/Inf
                                nan_count = np.isnan(actual_plot_target_unscaled).sum()
                                inf_count = np.isinf(actual_plot_target_unscaled).sum()
                                logger.debug(f"检查未缩放历史目标值: NaN数量={nan_count}, Inf数量={inf_count}")

                                valid_indices = ~np.isnan(actual_plot_target_unscaled) & ~np.isinf(actual_plot_target_unscaled)
                                num_valid_hist = np.sum(valid_indices)
                                logger.debug(f"有效历史点数量: {num_valid_hist}")

                                if num_valid_hist > 0:
                                    logger.info(f"准备绘制 {num_valid_hist} 个有效的历史点...")
                                    logger.debug("调用 ax.plot 绘制历史数据...")
                                    ax.plot(actual_plot_timestamps[valid_indices], actual_plot_target_unscaled[valid_indices],
                                            'b.-', markersize=5, linewidth=1, label=f'实际值 (最近 {plot_range} 步)')
                                    logger.debug("ax.plot 绘制历史数据调用完成。")
                                    plot_title = f'最近 {num_historical_steps} 步实际值与单步预测 (ID: {self.current_model_id or "N/A"})'
                                else:
                                    logger.warning("没有有效的历史数据点可供绘制。")
                                    ax.text(0.5, 0.6, "无有效历史数据", color='orange', ha='center', transform=ax.transAxes)
                        except Exception as e:
                            logger.error(f"历史数据：使用旧版本scaler进行反向变换时出错: {e}", exc_info=True)
                            # 使用简单的线性映射作为回退
                            target_scaled_data = self.data[target_col].iloc[-plot_range:].values

                            # 根据数据类型和目标列选择合适的映射
                            if CURRENT_DATA_TYPE == 'STOCK':
                                if target_col == 'volume':
                                    actual_plot_target_unscaled = target_scaled_data * 1000000  # 假设成交量在0-1000000之间
                                else:
                                    actual_plot_target_unscaled = target_scaled_data * 100  # 假设价格在0-100之间
                            else:
                                actual_plot_target_unscaled = target_scaled_data * 50  # 默认映射到0-50

                            logger.warning(f"历史数据：使用应急方法计算的未缩放值")

                            # 检查NaN/Inf
                            nan_count = np.isnan(actual_plot_target_unscaled).sum()
                            inf_count = np.isinf(actual_plot_target_unscaled).sum()
                            logger.debug(f"检查未缩放历史目标值: NaN数量={nan_count}, Inf数量={inf_count}")

                            valid_indices = ~np.isnan(actual_plot_target_unscaled) & ~np.isinf(actual_plot_target_unscaled)
                            num_valid_hist = np.sum(valid_indices)
                            logger.debug(f"有效历史点数量: {num_valid_hist}")

                            if num_valid_hist > 0:
                                logger.info(f"准备绘制 {num_valid_hist} 个有效的历史点...")
                                logger.debug("调用 ax.plot 绘制历史数据...")
                                ax.plot(actual_plot_timestamps[valid_indices], actual_plot_target_unscaled[valid_indices],
                                        'b.-', markersize=5, linewidth=1, label=f'实际值 (最近 {plot_range} 步)')
                                logger.debug("ax.plot 绘制历史数据调用完成。")
                                plot_title = f'最近 {num_historical_steps} 步实际值与单步预测 (ID: {self.current_model_id or "N/A"})'
                            else:
                                logger.warning("没有有效的历史数据点可供绘制。")
                                ax.text(0.5, 0.6, "无有效历史数据", color='orange', ha='center', transform=ax.transAxes)

                        except Exception as e:
                            logger.error(f"处理或绘制历史数据时发生错误: {e}", exc_info=True)
                            ax.text(0.5, 0.6, "无法绘制历史数据", color='orange', ha='center', transform=ax.transAxes)
                    else:
                        logger.error(f"无法找到目标列 {target_col} 对应的归一化器，无法绘制历史数据")
                        ax.text(0.5, 0.6, "无法绘制历史数据：找不到归一化器", color='orange', ha='center', transform=ax.transAxes)
                else:
                    logger.warning("计算的历史绘图范围为零，跳过历史绘图。")
            else:
                logger.warning("show_historical 条件为 False，跳过绘制历史数据。")

            # --- 绘制预测点 ---
            is_pred_valid = not (math.isnan(prediction_unscaled) or math.isinf(prediction_unscaled))
            if is_pred_valid:
                logger.info(f"在 {prediction_timestamp} 绘制预测点: {prediction_unscaled:.2f}")
                ax.plot(prediction_timestamp, prediction_unscaled, 'ro', markersize=8,
                        label=f'预测值\n({prediction_unscaled:.2f})')
            else:
                logger.error("预测值为 NaN 或 Inf，无法绘制点。")
                y_lim = ax.get_ylim()
                y_pos = y_lim[0] + (y_lim[1] - y_lim[0]) * 0.1
                ax.text(prediction_timestamp, y_pos, "预测NaN/Inf", color='red', ha='center', fontsize=9)

            # --- 设置坐标轴和标题 ---
            ax.set_title(plot_title, fontsize=11)
            ax.set_xlabel('时间', fontsize=10)
            ax.set_ylabel(f'{target_col} ({original_target_name})', fontsize=10)
            ax.legend(fontsize='medium')
            ax.grid(True, linestyle=':', alpha=0.7)
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
            ax.figure.autofmt_xdate(rotation=15, ha='right')
            ax.tick_params(axis='both', labelsize=8)

            # --- 自动调整 Y 轴范围 ---
            ax.relim()
            ax.autoscale_view(True, True, True)

            # --- 重新绘制 Canvas ---
            logger.info("绘制完成，调用 self.data_canvas.draw()。")
            self.data_canvas.draw() # 绘制更新后的图表
            self.update_status_bar(f"单步预测完成: {prediction_unscaled:.2f}", 5000)
            self.tab_widget.setCurrentWidget(self.tab_data_viz)

            # --- 保存预测到数据库 (保持不变) ---
            if self.current_model_id is not None:
                try:
                    self.db_manager.insert_prediction(
                        model_id=self.current_model_id,
                        target_timestamp=prediction_timestamp.to_pydatetime(),
                        predicted_value=prediction_unscaled,
                        actual_value=None
                    )
                    logger.info(f"模型 {self.current_model_id} 的预测已保存到数据库。")
                except Exception as db_err:
                    logger.error(f"将预测保存到数据库失败: {db_err}", exc_info=True)
                    QMessageBox.warning(self, "数据库写入警告", f"无法将预测结果保存到数据库:\n{db_err}")
            else:
                logger.warning("没有活动模型 ID。预测未保存到数据库。")

        except Exception as e:
            error_msg = f"单步预测失败: {str(e)}"
            logger.error(error_msg, exc_info=True) # 使用 exc_info=True 获取完整 Traceback
            QMessageBox.critical(self, "预测错误", f"{error_msg}\n详细信息请查看日志。") # 提示用户看日志
            self.update_status_bar(f"错误: {error_msg[:100]}...", 10000)
            # 清理绘图区域以显示错误消息
            try:
                self.data_fig.clf()
                ax = self.data_fig.add_subplot(111)
                ax.text(0.5, 0.5, '单步预测失败', color='red', ha='center', va='center', transform=ax.transAxes)
                self.data_canvas.draw()
            except Exception as clear_err:
                 logger.error(f"清理图形后出现错误: {clear_err}")

    def _scale_value(self, col_name, value):
        """根据列名和值，返回其 scaled 值。支持新的scalers结构。"""
        # 检查列是否在minmax_cols中
        if hasattr(self, 'minmax_cols') and col_name in self.minmax_cols and hasattr(self, 'scalers') and 'minmax_scaler' in self.scalers:
            try:
                # 获取列在minmax_cols中的索引
                idx_in_cols = self.minmax_cols.index(col_name)

                # 创建一个虚拟DataFrame行
                dummy_data = np.zeros((1, len(self.minmax_cols)))
                df_to_transform = pd.DataFrame(dummy_data, columns=self.minmax_cols)
                df_to_transform.loc[0, col_name] = float(value)  # 设置要缩放的值

                # 使用minmax_scaler进行缩放
                scaled_array = self.scalers['minmax_scaler'].transform(df_to_transform)
                scaled_value_for_col = scaled_array[0, idx_in_cols]
                return scaled_value_for_col
            except ValueError as ve:
                logger.error(f"ValueError 缩放列 '{col_name}' (值: {value}): {ve}. 返回 NaN.")
                return np.nan
            except Exception as e:
                logger.error(f"缩放列 '{col_name}' (值: {value}) 时发生未知错误: {e}", exc_info=True)
                return np.nan

        # 检查列是否在robust_cols中
        elif hasattr(self, 'robust_cols') and col_name in self.robust_cols and hasattr(self, 'scalers') and 'robust_scaler' in self.scalers:
            try:
                # 获取列在robust_cols中的索引
                idx_in_cols = self.robust_cols.index(col_name)

                # 创建一个虚拟DataFrame行
                dummy_data = np.zeros((1, len(self.robust_cols)))
                df_to_transform = pd.DataFrame(dummy_data, columns=self.robust_cols)
                df_to_transform.loc[0, col_name] = float(value)  # 设置要缩放的值

                # 使用robust_scaler进行缩放
                scaled_array = self.scalers['robust_scaler'].transform(df_to_transform)
                scaled_value_for_col = scaled_array[0, idx_in_cols]
                return scaled_value_for_col
            except ValueError as ve:
                logger.error(f"ValueError 缩放列 '{col_name}' (值: {value}): {ve}. 返回 NaN.")
                return np.nan
            except Exception as e:
                logger.error(f"缩放列 '{col_name}' (值: {value}) 时发生未知错误: {e}", exc_info=True)
                return np.nan

        # 兼容旧版本 - 使用单一scaler
        elif hasattr(self, 'scaler') and self.scaler is not None and hasattr(self, 'numeric_cols') and col_name in self.numeric_cols:
            try:
                # 获取列在numeric_cols中的索引
                idx_in_numeric_cols = self.numeric_cols.index(col_name)

                # 创建一个虚拟DataFrame行
                dummy_data = np.zeros((1, len(self.numeric_cols)))
                df_to_transform = pd.DataFrame(dummy_data, columns=self.numeric_cols)
                df_to_transform.loc[0, col_name] = float(value)  # 设置要缩放的值

                # 使用scaler进行缩放
                scaled_array = self.scaler.transform(df_to_transform)
                scaled_value_for_col = scaled_array[0, idx_in_numeric_cols]
                return scaled_value_for_col
            except ValueError as ve:
                logger.error(f"ValueError 缩放列 '{col_name}' (值: {value}): {ve}. 返回 NaN.")
                return np.nan
            except Exception as e:
                logger.error(f"缩放列 '{col_name}' (值: {value}) 时发生未知错误: {e}", exc_info=True)
                return np.nan

        # 如果列不参与归一化，返回原始值
        else:
            # 检查是否是周期性特征或分类特征
            if hasattr(self, 'periodic_cols') and col_name in self.periodic_cols:
                return value
            elif hasattr(self, 'categorical_cols') and col_name in self.categorical_cols:
                return value
            elif col_name in ['day_sin', 'day_cos', 'month_sin', 'month_cos', 'hour_sin', 'hour_cos', 'weekday_sin', 'weekday_cos', 'hour', 'weekday', 'month']:
                return value
            else:
                logger.warning(f"找不到列 '{col_name}' 对应的归一化器。返回原始值: {value}")
                return value

    def _unscale_value(self, col_name, scaled_value):
        """根据列名和 scaled 值，返回其 unscaled 值。支持新的scalers结构。"""
        # 详细记录反归一化输入
        logger.debug(f"开始反归一化: 列='{col_name}', 缩放值={scaled_value:.6f}")

        # 应急反归一化方法 - 当其他方法失败时使用
        def emergency_unscale(col, val):
            """应急反归一化方法，基于数据类型和列名进行简单线性映射"""
            if col == 'power_consumption' or col == 'DOM_MW':
                # 能源数据的功率消耗通常在几千到几万之间
                return val * 10000.0  # 假设归一化值在0-1之间，映射到0-10000
            elif 'temperature' in col:
                return val * 40.0 - 10.0  # 映射到-10到30摄氏度
            elif 'humidity' in col:
                return val * 100.0  # 映射到0-100%
            elif 'pressure' in col:
                return val * 300.0 + 800.0  # 映射到800-1100百帕
            elif 'price' in col:
                return val * 100.0  # 映射到0-100价格单位
            elif 'volume' in col:
                return val * 1000000.0  # 映射到0-1000000交易量
            else:
                # 默认映射
                return val * 100.0  # 映射到0-100

        # 如果缩放器不可用，直接使用应急方法
        no_scalers = (not hasattr(self, 'scalers') or self.scalers is None)
        no_minmax = (not hasattr(self, 'scalers') or 'minmax_scaler' not in self.scalers or self.scalers['minmax_scaler'] is None)
        no_robust = (not hasattr(self, 'scalers') or 'robust_scaler' not in self.scalers or self.scalers['robust_scaler'] is None)
        no_old_scaler = (not hasattr(self, 'scaler') or self.scaler is None)

        if (no_scalers or (no_minmax and no_robust)) and no_old_scaler:
            unscaled = emergency_unscale(col_name, scaled_value)
            logger.warning(f"缩放器不可用，使用应急方法反归一化 {col_name}: {scaled_value:.4f} -> {unscaled:.4f}")
            return unscaled

        # 检查列是否在minmax_cols中
        if hasattr(self, 'minmax_cols') and col_name in self.minmax_cols and hasattr(self, 'scalers') and 'minmax_scaler' in self.scalers:
            try:
                # 获取列在minmax_cols中的索引
                idx_in_cols = self.minmax_cols.index(col_name)
                logger.debug(f"使用 minmax_scaler 反归一化, 列索引={idx_in_cols}, 列总数={len(self.minmax_cols)}")

                # 检查scaler的形状
                scaler_n_features = self.scalers['minmax_scaler'].scale_.shape[0]

                # 如果scaler的特征数量与minmax_cols不匹配，使用简化的方法
                if scaler_n_features != len(self.minmax_cols):
                    logger.warning(f"反归一化：scaler特征数量 ({scaler_n_features}) 与minmax_cols长度 ({len(self.minmax_cols)}) 不匹配")
                    logger.warning("反归一化：使用简化的反向变换方法")

                    # 使用简化的方法：手动计算反向变换
                    # 假设MinMaxScaler使用公式: X_std * (max - min) + min
                    feature_min = self.scalers['minmax_scaler'].min_[0]  # 使用第一个特征的最小值
                    feature_max = feature_min + self.scalers['minmax_scaler'].scale_[0]  # min + scale = max

                    # 手动计算反向变换
                    unscaled_value_for_col = scaled_value * (feature_max - feature_min) + feature_min
                    logger.info(f"使用简化方法计算的未缩放值: {unscaled_value_for_col:.4f}")
                else:
                    # 创建一个虚拟DataFrame行
                    dummy_data = np.zeros((1, scaler_n_features))
                    cols_to_use = self.minmax_cols[:scaler_n_features]  # 确保长度匹配
                    df_to_transform = pd.DataFrame(dummy_data, columns=cols_to_use)

                    # 确保列名在cols_to_use中
                    if col_name in cols_to_use:
                        df_to_transform.loc[0, col_name] = float(scaled_value)  # 设置要反归一化的值
                        idx_in_cols = cols_to_use.index(col_name)
                    else:
                        # 如果列名不在cols_to_use中，使用第一列
                        logger.warning(f"列名 '{col_name}' 不在cols_to_use中，使用第一列")
                        df_to_transform.iloc[0, 0] = float(scaled_value)
                        idx_in_cols = 0

                    # 记录反归一化前的DataFrame
                    logger.debug(f"反归一化前的DataFrame (仅显示非零值): {df_to_transform.loc[0, df_to_transform.loc[0] != 0].to_dict()}")

                    # 使用minmax_scaler进行反归一化
                    unscaled_array = self.scalers['minmax_scaler'].inverse_transform(df_to_transform)
                    unscaled_value_for_col = unscaled_array[0, idx_in_cols]

                    # 记录反归一化结果
                    logger.debug(f"反归一化结果: {unscaled_value_for_col:.6f}")

                    # 验证反归一化的正确性
                    test_back = self._scale_value(col_name, unscaled_value_for_col)
                    logger.debug(f"验证: 将反归一化结果再次归一化: {test_back:.6f}, 原始缩放值: {scaled_value:.6f}, 差异: {abs(test_back - scaled_value):.10f}")

                return unscaled_value_for_col
            except ValueError as ve:
                logger.error(f"ValueError 反归一化列 '{col_name}' (scaled_value: {scaled_value}): {ve}")
                # 使用应急方法作为备选
                unscaled = emergency_unscale(col_name, scaled_value)
                logger.warning(f"反归一化失败 (ValueError)，使用应急方法: {scaled_value:.4f} -> {unscaled:.4f}")
                return unscaled
            except Exception as e:
                logger.error(f"反归一化列 '{col_name}' (scaled_value: {scaled_value}) 时发生未知错误: {e}", exc_info=True)
                # 使用应急方法作为备选
                unscaled = emergency_unscale(col_name, scaled_value)
                logger.warning(f"反归一化失败，使用应急方法: {scaled_value:.4f} -> {unscaled:.4f}")
                return unscaled

        # 检查列是否在robust_cols中
        elif hasattr(self, 'robust_cols') and col_name in self.robust_cols and hasattr(self, 'scalers') and 'robust_scaler' in self.scalers:
            try:
                # 获取列在robust_cols中的索引
                idx_in_cols = self.robust_cols.index(col_name)

                # 检查scaler的形状
                scaler_n_features = self.scalers['robust_scaler'].center_.shape[0]

                # 如果scaler的特征数量与robust_cols不匹配，使用简化的方法
                if scaler_n_features != len(self.robust_cols):
                    logger.warning(f"反归一化：scaler特征数量 ({scaler_n_features}) 与robust_cols长度 ({len(self.robust_cols)}) 不匹配")
                    logger.warning("反归一化：使用简化的反向变换方法")

                    # 使用简化的方法：手动计算反向变换
                    # RobustScaler使用公式: X_scaled * scale_ + center_
                    center = self.scalers['robust_scaler'].center_[0]  # 使用第一个特征的中心值
                    scale = self.scalers['robust_scaler'].scale_[0]    # 使用第一个特征的缩放因子

                    # 手动计算反向变换
                    unscaled_value_for_col = scaled_value * scale + center
                    logger.info(f"使用简化方法计算的未缩放值: {unscaled_value_for_col:.4f}")
                else:
                    # 创建一个虚拟DataFrame行
                    dummy_data = np.zeros((1, scaler_n_features))
                    cols_to_use = self.robust_cols[:scaler_n_features]  # 确保长度匹配
                    df_to_transform = pd.DataFrame(dummy_data, columns=cols_to_use)

                    # 确保列名在cols_to_use中
                    if col_name in cols_to_use:
                        df_to_transform.loc[0, col_name] = float(scaled_value)  # 设置要反归一化的值
                        idx_in_cols = cols_to_use.index(col_name)
                    else:
                        # 如果列名不在cols_to_use中，使用第一列
                        logger.warning(f"列名 '{col_name}' 不在cols_to_use中，使用第一列")
                        df_to_transform.iloc[0, 0] = float(scaled_value)
                        idx_in_cols = 0

                    # 使用robust_scaler进行反归一化
                    unscaled_array = self.scalers['robust_scaler'].inverse_transform(df_to_transform)
                    unscaled_value_for_col = unscaled_array[0, idx_in_cols]

                return unscaled_value_for_col
            except ValueError as ve:
                logger.error(f"ValueError 反归一化列 '{col_name}' (scaled_value: {scaled_value}): {ve}")
                # 使用应急方法作为备选
                unscaled = emergency_unscale(col_name, scaled_value)
                logger.warning(f"反归一化失败 (ValueError)，使用应急方法: {scaled_value:.4f} -> {unscaled:.4f}")
                return unscaled
            except Exception as e:
                logger.error(f"反归一化列 '{col_name}' (scaled_value: {scaled_value}) 时发生未知错误: {e}", exc_info=True)
                # 使用应急方法作为备选
                unscaled = emergency_unscale(col_name, scaled_value)
                logger.warning(f"反归一化失败，使用应急方法: {scaled_value:.4f} -> {unscaled:.4f}")
                return unscaled

        # 兼容旧版本 - 使用单一scaler
        elif hasattr(self, 'scaler') and self.scaler is not None and hasattr(self, 'numeric_cols') and col_name in self.numeric_cols:
            try:
                # 获取列在numeric_cols中的索引
                idx_in_numeric_cols = self.numeric_cols.index(col_name)

                # 检查scaler是否可用
                if self.scaler is None:
                    raise ValueError("scaler对象为None")

                # 检查形状是否匹配
                expected_shape = len(self.numeric_cols)

                # 获取scaler的特征数量
                if hasattr(self.scaler, 'scale_'):
                    scaler_n_features = self.scaler.scale_.shape[0]
                elif hasattr(self.scaler, 'n_features_in_'):
                    scaler_n_features = self.scaler.n_features_in_
                else:
                    # 如果无法确定scaler的特征数量，假设它与numeric_cols匹配
                    scaler_n_features = expected_shape

                # 如果scaler的特征数量与numeric_cols不匹配，使用简化的方法
                if scaler_n_features != expected_shape:
                    logger.warning(f"反归一化：scaler特征数量 ({scaler_n_features}) 与numeric_cols长度 ({expected_shape}) 不匹配")
                    logger.warning("反归一化：使用简化的反向变换方法")

                    # 使用简单的线性映射作为回退
                    if CURRENT_DATA_TYPE == 'STOCK':
                        if col_name == 'volume':
                            unscaled_value_for_col = scaled_value * 1000000  # 假设成交量在0-1000000之间
                        elif col_name in ['open_price', 'close_price', 'high_price', 'low_price']:
                            unscaled_value_for_col = scaled_value * 100  # 假设价格在0-100之间
                        else:
                            unscaled_value_for_col = scaled_value * 50  # 默认映射到0-50
                    elif CURRENT_DATA_TYPE == 'WEATHER':
                        if col_name == 'temperature':
                            unscaled_value_for_col = scaled_value * 40 - 10  # 假设温度在-10到30之间
                        elif col_name == 'humidity':
                            unscaled_value_for_col = scaled_value * 100  # 假设湿度在0-100之间
                        elif col_name == 'pressure':
                            unscaled_value_for_col = scaled_value * 300 + 800  # 假设气压在800-1100之间
                        else:
                            unscaled_value_for_col = scaled_value * 50  # 默认映射到0-50
                    else:
                        unscaled_value_for_col = scaled_value * 100  # 默认映射到0-100

                    logger.info(f"使用简化方法计算的未缩放值: {unscaled_value_for_col:.4f}")
                else:
                    # 创建一个虚拟DataFrame行
                    dummy_data = np.zeros((1, scaler_n_features))
                    df_to_transform = pd.DataFrame(dummy_data, columns=self.numeric_cols[:scaler_n_features])

                    # 确保列名在df_to_transform.columns中
                    if col_name in df_to_transform.columns:
                        df_to_transform.loc[0, col_name] = float(scaled_value)  # 设置要反归一化的值
                        idx_in_numeric_cols = list(df_to_transform.columns).index(col_name)
                    else:
                        # 如果列名不在df_to_transform.columns中，使用第一列
                        logger.warning(f"列名 '{col_name}' 不在df_to_transform.columns中，使用第一列")
                        df_to_transform.iloc[0, 0] = float(scaled_value)
                        idx_in_numeric_cols = 0

                    # 使用scaler进行反归一化
                    unscaled_array = self.scaler.inverse_transform(df_to_transform)
                    unscaled_value_for_col = unscaled_array[0, idx_in_numeric_cols]

                return unscaled_value_for_col
            except ValueError as ve:
                logger.error(f"ValueError 反归一化列 '{col_name}' (scaled_value: {scaled_value}): {ve}")
                # 使用应急方法作为备选
                unscaled = emergency_unscale(col_name, scaled_value)
                logger.warning(f"反归一化失败 (ValueError)，使用应急方法: {scaled_value:.4f} -> {unscaled:.4f}")
                return unscaled
            except Exception as e:
                logger.error(f"反归一化列 '{col_name}' (scaled_value: {scaled_value}) 时发生未知错误: {e}", exc_info=True)
                # 使用应急方法作为备选
                unscaled = emergency_unscale(col_name, scaled_value)
                logger.warning(f"反归一化失败，使用应急方法: {scaled_value:.4f} -> {unscaled:.4f}")
                return unscaled

        # 如果列不参与归一化，返回原始值
        else:
            # 检查是否是周期性特征或分类特征
            if hasattr(self, 'periodic_cols') and col_name in self.periodic_cols:
                return scaled_value
            elif hasattr(self, 'categorical_cols') and col_name in self.categorical_cols:
                return scaled_value
            elif col_name in ['day_sin', 'day_cos', 'month_sin', 'month_cos', 'hour_sin', 'hour_cos', 'weekday_sin', 'weekday_cos', 'hour', 'weekday', 'month']:
                return scaled_value
            else:
                logger.warning(f"找不到列 '{col_name}' 对应的归一化器。返回原始缩放值: {scaled_value}")
                return scaled_value

    # 新增一个槽函数来处理按钮点击和参数传递
    def call_predict_next_day_recursive(self):
        logger.info("--- “预测下一天”按钮被点击 ---") # 增加日志
        use_oracle = self.oracle_mode_checkbox.isChecked()
        logger.debug(f"  Oracle模式复选框状态: {use_oracle}")
        self.predict_next_day_recursive(use_oracle_non_targets=use_oracle)

    def predict_next_day_recursive(self, use_oracle_non_targets=False):
        """
        进行多步递归预测（股票数据5步，其他数据类型50步），并将结果绘制。
        Args:
            use_oracle_non_targets (bool): 是否对非目标特征使用真实的未来值进行调试。
                                           需要 self.featured_df_before_norm 包含相应的未来数据。
        """
        if use_oracle_non_targets:
            logger.info("尝试进行多步递归预测 (Oracle 非目标特征模式)...")
        else:
            logger.info("尝试进行多步递归预测 (自定义特征更新策略模式)...")

        # --- Scaler Check ---
        logger.info("--- 进入 Scaler 检查 ---")
        if self.target_col in self.numeric_col_indices_map and \
                self.data is not None and not self.data.empty and \
                self.featured_df_before_norm is not None and not self.featured_df_before_norm.empty:
            try:
                last_timestamp_in_self_data = self.data.index[-1]
                s_check_scaled = self.data.loc[last_timestamp_in_self_data, self.target_col]
                logger.debug(f"  Scaler Check: 最后一个训练/验证点时间戳: {last_timestamp_in_self_data}, "
                             f"其缩放目标值 ({self.target_col}): {s_check_scaled:.4f}")
                s_check_unscaled_func = self._unscale_value(self.target_col, s_check_scaled)
                logger.debug(f"  Scaler Check: 反归一化后的值: {s_check_unscaled_func:.4f}")
                if last_timestamp_in_self_data in self.featured_df_before_norm.index:
                    s_check_original = self.featured_df_before_norm.loc[last_timestamp_in_self_data, self.target_col]
                    logger.debug(
                        f"  Scaler Check: 对应原始值 (来自 featured_df_before_norm at {last_timestamp_in_self_data}): {s_check_original:.4f}")
                    if abs(s_check_unscaled_func - s_check_original) > 1e-3:
                        logger.error(
                            f"SCALER MISMATCH! 反归一化: {s_check_unscaled_func:.4f}, 原始: {s_check_original:.4f} (在时间点 {last_timestamp_in_self_data})")
                    else:
                        logger.debug(f"Scaler 检查通过 (在时间点 {last_timestamp_in_self_data})。")
                else:
                    logger.warning(f"  Scaler Check: 时间戳 {last_timestamp_in_self_data} (来自 self.data 末尾) "
                                   f"在 self.featured_df_before_norm 中找不到。无法进行精确的 Scaler Mismatch 检查。")
            except IndexError:
                logger.error("Scaler Check: 访问 .iloc[-1] 或 .loc 时发生 IndexError。可能是 self.data 为空。")
            except Exception as e_sc_check:
                logger.error(f"Scaler Check: 发生未知错误: {e_sc_check}", exc_info=True)
        else:
            logger.warning("Scaler Check: 一个或多个必要条件未满足 (target_col, data, featured_df_before_norm)。")
        logger.info("--- 退出 Scaler 检查 ---")

        # --- 属性和状态的初始日志打印 ---
        try:
            logger.debug(f"当前目标列: {self.target_col}")
            logger.debug(f"当前特征列 (feature_cols) ({len(self.feature_cols)}): {str(self.feature_cols)[:300]}...")
            logger.debug(
                f"参与MinMax归一化的列 (numeric_cols) ({len(self.numeric_cols)}): {str(self.numeric_cols)[:300]}...")
            if self.numeric_col_indices_map:
                self.numeric_col_indices_map = {k: v for k, v in self.numeric_col_indices_map.items()}
                logger.debug(f"numeric_col_indices_map 键 (前5个): {list(self.numeric_col_indices_map.keys())[:5]}")
            else:
                logger.warning("numeric_col_indices_map 为空或None!")
        except Exception as e_initial_log:
            logger.error(f"打印初始属性时出错: {e_initial_log}", exc_info=True)
            QMessageBox.critical(self, "内部错误", f"准备递归预测时出错: {e_initial_log}")
            return

        # --- 核心预检查 (更新以支持新的scalers结构) ---
        logger.debug("正在进行递归预测的核心预检查...")

        # 基本必需属性
        required_attrs = ['model', 'data', 'feature_cols', 'featured_df_before_norm', 'target_col_index_in_numeric', 'last_scaled_numeric_row']

        # 检查归一化器 - 支持新旧两种结构
        has_scaler = False
        if hasattr(self, 'scalers') and self.scalers is not None:
            # 新结构 - 使用scalers字典
            if 'minmax_scaler' in self.scalers and self.scalers['minmax_scaler'] is not None:
                has_scaler = True
                required_attrs.append('minmax_cols')
            elif 'robust_scaler' in self.scalers and self.scalers['robust_scaler'] is not None:
                has_scaler = True
                required_attrs.append('robust_cols')
        elif hasattr(self, 'scaler') and self.scaler is not None:
            # 旧结构 - 使用单一scaler
            has_scaler = True
            required_attrs.append('numeric_cols')
            required_attrs.append('numeric_col_indices_map')

        if not has_scaler:
            logger.warning("预测预检查失败：缺少归一化器。")
            QMessageBox.warning(self, "数据状态不完整", "缺少进行预测所需的归一化器。")
            return
        missing_attrs_list = []
        for attr_name in required_attrs:  # 已修正循环变量名
            val = getattr(self, attr_name, None)
            if val is None or \
                    (isinstance(val, (pd.DataFrame, list, dict, np.ndarray)) and not len(val) and not (
                            hasattr(val, 'size') and val.size > 0)):
                if isinstance(val, pd.DataFrame) and val.empty and attr_name not in ['featured_df_before_norm', 'data']:
                    pass
                else:
                    missing_attrs_list.append(attr_name)
        if self.data is None or self.data.empty: missing_attrs_list.append("data (empty_or_none)")
        if self.featured_df_before_norm is None or self.featured_df_before_norm.empty: missing_attrs_list.append(
            "featured_df_before_norm (empty_or_none)")
        if missing_attrs_list:
            error_msg = f"递归预测无法进行。缺失或为空的关键属性: {', '.join(missing_attrs_list)}"
            logger.error(error_msg)
            QMessageBox.warning(self, "状态不完整", f"{error_msg}\n请确保数据已加载且模型已训练/加载。")
            return
        logger.debug("核心预检查通过。")

        seq_length = self.seq_length
        if len(self.data) < seq_length:
            QMessageBox.warning(self, "数据不足", f"归一化数据点 ({len(self.data)}) 少于序列长度 ({seq_length})。")
            return
        logger.debug("数据长度检查通过。")

        self.update_status_bar(f"正在进行递归预测 ({'Oracle模式' if use_oracle_non_targets else '自定义策略'})...", 0)
        QApplication.processEvents()  # 确保UI更新

        predicted_timestamps = []
        predicted_values_unscaled = []
        steps_completed = 0

        try:  # 主 try 块
            target_col = self.target_col
            device = self.get_selected_device()
            self.model.eval()
            self.model.to(device)
            logger.info(f"模型已设为评估模式并移至设备: {device}")

            # 计算时间间隔和预测步数
            time_interval = pd.Timedelta(minutes=10)
            if len(self.data.index) >= 2:
                calculated_interval = self.data.index[-1] - self.data.index[-2]
                if calculated_interval.total_seconds() > 0: time_interval = calculated_interval
            seconds_per_step = time_interval.total_seconds()

            # 根据数据类型调整预测步数
            if CURRENT_DATA_TYPE == 'STOCK':
                # 对于股票数据，预测5个交易日
                num_prediction_steps = 5
            else:
                # 对于其他数据类型，固定预测50步
                num_prediction_steps = 50

            logger.info(f"预测参数: 时间间隔={time_interval}, 总步数={num_prediction_steps}")
            if time_interval.total_seconds() <= 0:
                logger.error(f"错误：计算得到的时间间隔无效: {time_interval}。")
                QMessageBox.critical(self, "内部错误", "时间间隔无效，无法递归预测。")
                return

            if self.data is None or self.data.empty:
                logger.error("错误: self.data 为空或None，无法获取 last_known_timestamp。")
                QMessageBox.critical(self, "数据错误", "无法确定递归预测的起始点，数据为空。")
                return
            last_known_timestamp = self.data.index[-1]
            current_timestamp = last_known_timestamp
            logger.info(f"递归预测将从 {last_known_timestamp.strftime('%Y-%m-%d %H:%M:%S')} 之后开始。")

            # 计算缓冲区长度
            buffer_len_rolling_calc = self.seq_length + (6 * 6) + 10
            # 查找时间戳的索引
            try:
                end_index_loc = self.featured_df_before_norm.index.get_loc(last_known_timestamp)
            except KeyError:
                logger.error(
                    f"错误：last_known_timestamp ({last_known_timestamp}) 在 self.featured_df_before_norm 的索引中找不到！")
                QMessageBox.critical(self, "数据错误", "递归预测的起始时间戳与基础数据不匹配。")
                return

            # 起始索引
            start_index_loc = max(0, end_index_loc - buffer_len_rolling_calc + 1)
            # 历史数据
            history_df_unscaled_iter = self.featured_df_before_norm.iloc[start_index_loc: end_index_loc + 1].copy()
            if len(history_df_unscaled_iter) < buffer_len_rolling_calc:
                logger.warning(
                    f"初始化 history_df_unscaled_iter 时，实际可用历史 ({len(history_df_unscaled_iter)}) 少于期望 ({buffer_len_rolling_calc})。")
            # 设置索引为日期时间格式
            history_df_unscaled_iter.index = pd.to_datetime(history_df_unscaled_iter.index)
            logger.debug(
                f"初始化未归一化历史迭代缓冲区 (history_df_unscaled_iter)，形状: {history_df_unscaled_iter.shape}, 时间范围: {history_df_unscaled_iter.index.min()} to {history_df_unscaled_iter.index.max()}")
            if not history_df_unscaled_iter.index.is_monotonic_increasing:
                logger.error("FATAL: history_df_unscaled_iter 初始化后索引不单调！检查 featured_df_before_norm 的索引。")
                return

            current_scaled_feature_sequence = self.data[self.feature_cols].values[-seq_length:].tolist()
            logger.debug(
                f"初始化当前缩放特征序列 (current_scaled_feature_sequence)，长度: {len(current_scaled_feature_sequence)}")

            last_actual_target_scaled_value_for_debug = self.data[target_col].iloc[
                -1] if target_col in self.data.columns else np.nan
            logger.debug(
                f"最后一个实际目标 ({target_col}) 的缩放值 (来自self.data的末尾): {last_actual_target_scaled_value_for_debug:.6f}")


            logger.debug("准备进入递归预测循环...")
            for step in range(num_prediction_steps):
                logger.debug(f"--- 递归步骤 {step + 1}/{num_prediction_steps} ---")
                if len(current_scaled_feature_sequence) < seq_length:
                    logger.error(
                        f"current_scaled_feature_sequence 数据不足! 需要 {seq_length}, 实际 {len(current_scaled_feature_sequence)}。停止。")
                    break

                input_sequence_np = np.array(current_scaled_feature_sequence,
                                             dtype=np.float32)  # 使用整个列表，它应该总是seq_length长
                if input_sequence_np.shape != (seq_length, len(self.feature_cols)):
                    logger.error(
                        f"步骤 {step + 1} input_sequence_np 形状错误! 期望: {(seq_length, len(self.feature_cols))}, 实际: {input_sequence_np.shape}。停止。")
                    break
                # unsqueeze(0) 增加维度
                input_tensor = torch.FloatTensor(input_sequence_np).unsqueeze(0).to(device)

                if step < 2 or (step + 1) % 72 == 0:
                    logger.debug(
                        f"  步骤 {step + 1} input_tensor (送入模型前) (前2时间步，前3特征):\n{input_tensor[0, :2, :3].cpu().numpy()}")

                # 详细记录输入张量的统计信息
                input_mean = input_tensor.mean().cpu().item()
                input_std = input_tensor.std().cpu().item()
                input_min = input_tensor.min().cpu().item()
                input_max = input_tensor.max().cpu().item()

                if step < 5 or (step + 1) % 24 == 0:
                    logger.debug(f"  步骤 {step + 1} 输入张量统计: 均值={input_mean:.6f}, 标准差={input_std:.6f}, 最小值={input_min:.6f}, 最大值={input_max:.6f}")

                # 记录目标列在输入序列中的值（如果目标列也是特征之一）
                if target_col in self.feature_cols:
                    target_idx = self.feature_cols.index(target_col)
                    target_values = input_tensor[0, -5:, target_idx].cpu().numpy()  # 获取最后5个时间步的目标值
                    if step < 5 or (step + 1) % 24 == 0:
                        logger.debug(f"  步骤 {step + 1} 输入序列中的目标列 ({target_col}) 最后5个值: {target_values}")

                with torch.no_grad():  # 模型预测
                    scaled_target_prediction = self.model(input_tensor).squeeze().cpu().item()

                # 增强预测值日志记录
                if step < 5 or (step + 1) % 24 == 0:
                    log_msg_pred = f"  步骤 {step + 1} 缩放后预测值 ({target_col}): {scaled_target_prediction:.6f}"
                    if step == 0 and not np.isnan(last_actual_target_scaled_value_for_debug):
                        log_msg_pred += f" (对比历史最后一个缩放值: {last_actual_target_scaled_value_for_debug:.6f})"
                        # 计算差异百分比
                        if last_actual_target_scaled_value_for_debug != 0:
                            diff_percent = (scaled_target_prediction - last_actual_target_scaled_value_for_debug) / last_actual_target_scaled_value_for_debug * 100
                            log_msg_pred += f", 差异: {diff_percent:.2f}%"
                    logger.debug(log_msg_pred)

                # 3. 计算下一个时间戳
                next_timestamp = current_timestamp + time_interval
                predicted_timestamps.append(next_timestamp)
                unscaled_target_prediction = self._unscale_value(target_col, scaled_target_prediction)
                if np.isnan(unscaled_target_prediction):
                    logger.error(f"反归一化目标 ({target_col}) 值为 NaN! scaled={scaled_target_prediction}。停止。")
                    break
                predicted_values_unscaled.append(unscaled_target_prediction)
                if step < 2 or (step + 1) % 72 == 0:
                    logger.debug(
                        f"  步骤 {step + 1} 反归一化目标值 ({target_col}): {unscaled_target_prediction:.4f} @ {next_timestamp.strftime('%Y-%m-%d %H:%M:%S')}")

                # 5. 更新历史数据并重新计算特征 (在未缩放的迭代DataFrame上操作)
                new_row_unscaled_data = {}

                #   a. 目标列：使用刚预测的 unscaled_target_prediction
                new_row_unscaled_data[target_col] = unscaled_target_prediction

                #   b. 非目标列：根据 use_oracle_non_targets 决定策略
                if use_oracle_non_targets:
                    # Oracle 只预测目标变量，其他特征使用真实值
                    # 确保在 oracle_row is None 时，回退逻辑能正确处理所有 f_col_fill
                    # 并且 new_row_unscaled_data 最终包含所有 history_df_unscaled_iter.columns 中的列
                    oracle_row = self.featured_df_before_norm.loc[
                        next_timestamp] if next_timestamp in self.featured_df_before_norm.index else None
                    if oracle_row is None and step < 5: logger.warning(
                        f"    Oracle模式: 真实数据在 {next_timestamp} 不可用，将使用回退。")

                    for f_col_iter in history_df_unscaled_iter.columns:  # 确保新行有所有必要的列
                        if f_col_iter == target_col: continue  # 目标列已填充
                        if oracle_row is not None and f_col_iter in oracle_row:
                            new_row_unscaled_data[f_col_iter] = oracle_row[f_col_iter]
                        else:  # Oracle不可用或列不在Oracle行中，使用最后一个已知值回退
                            new_row_unscaled_data[f_col_iter] = history_df_unscaled_iter[f_col_iter].iloc[-1]
                else:
                    # 使用自定义的趋势外推/均值回退策略
                    last_u_row = history_df_unscaled_iter.iloc[-1]
                    second_last_u_row = history_df_unscaled_iter.iloc[-2] if len(
                        history_df_unscaled_iter) >= 2 else None
                    mean_win = min(6, len(history_df_unscaled_iter))

                    for f_col_iter in history_df_unscaled_iter.columns: # 确保新行有所有必要的列
                        if f_col_iter == target_col: continue

                        # 排除已由 time_features_for_new_step 覆盖的、派生特征、滚动特征
                        # 这些会在后面专门计算和填充
                        if f_col_iter in ['hour', 'day_sin', 'day_cos', 'weekday', 'month', 'month_sin', 'month_cos',
                                          'heat_index', 'wind_chill'] or '_6h_' in f_col_iter:
                            continue

                        # 对原始测量特征进行趋势/均值更新 + 裁剪
                        val_to_set, trend_applied = np.nan, False
                        if second_last_u_row is not None and f_col_iter in ['temperature',
                                                                            'dew_point']:  # 只对选定特征用趋势
                            trend = last_u_row[f_col_iter] - second_last_u_row[f_col_iter]
                            val_to_set = last_u_row[f_col_iter] + trend * 0.5  # decay_factor
                            trend_applied = True

                        if pd.isna(val_to_set) or not trend_applied:
                            val_to_set = history_df_unscaled_iter[f_col_iter].iloc[-mean_win:].mean()
                        if pd.isna(val_to_set): val_to_set = last_u_row.get(f_col_iter, np.nan)

                        # 裁剪
                        if f_col_iter == 'temperature':
                            val_to_set = np.clip(val_to_set, -50, 60)
                        elif f_col_iter == 'humidity':
                            val_to_set = np.clip(val_to_set, 0, 100)
                        elif f_col_iter == 'pressure':
                            val_to_set = np.clip(val_to_set, 800, 1100)
                        elif f_col_iter == 'wind_speed':
                            val_to_set = np.clip(val_to_set, 0, 50)
                        elif f_col_iter == 'dew_point':
                            temp_val_for_dew = new_row_unscaled_data.get('temperature', val_to_set)
                            val_to_set = np.clip(val_to_set, -60,
                                                 temp_val_for_dew if not pd.isna(temp_val_for_dew) else 40)
                        elif f_col_iter == 'precipitation':
                            val_to_set = np.clip(val_to_set, 0, 200)
                        new_row_unscaled_data[f_col_iter] = val_to_set

                #   c. 时间特征：为 next_timestamp 计算 (无论Oracle模式与否，时间特征总是新计算的)
                # 根据当前数据类型计算时间特征
                time_features = calculate_time_features_for_timestamp(next_timestamp, data_type=CURRENT_DATA_TYPE)
                new_row_unscaled_data.update(time_features)

                # 根据数据类型计算不同的派生特征
                if CURRENT_DATA_TYPE == 'WEATHER':
                    #   d. 重新计算气象派生特征 (如 heat_index, wind_chill)
                    temp_val = new_row_unscaled_data.get('temperature', np.nan)
                    hum_val = new_row_unscaled_data.get('humidity', np.nan)
                    ws_val = new_row_unscaled_data.get('wind_speed', np.nan)

                    # 计算热指数
                    if not pd.isna(temp_val) and not pd.isna(hum_val):
                        hi = 0.5 * (temp_val + 61.0 + (temp_val - 68.0) * 1.2 + hum_val * 0.094)
                        new_row_unscaled_data['heat_index'] = np.clip(hi, -40, 60)
                    else:
                        new_row_unscaled_data['heat_index'] = np.nan

                    # 计算风寒指数
                    if not pd.isna(temp_val) and not pd.isna(ws_val) and ws_val >= 0:
                        wc = temp_val if ws_val < 0.01 else (
                                    13.12 + 0.6215 * temp_val - 11.37 * (ws_val ** 0.16) + 0.3965 * temp_val * (
                                        ws_val ** 0.16))
                        new_row_unscaled_data['wind_chill'] = np.clip(wc, -70, temp_val if not pd.isna(temp_val) else wc)
                    else:
                        new_row_unscaled_data['wind_chill'] = np.nan

                elif CURRENT_DATA_TYPE == 'STOCK':
                    # 确保股票特有的特征存在
                    if 'is_trading_day' not in new_row_unscaled_data:
                        # 检查是否为交易日（周一至周五）
                        weekday = next_timestamp.weekday()
                        is_trading_day = 1 if weekday < 5 else 0
                        new_row_unscaled_data['is_trading_day'] = is_trading_day

                    if 'is_month_start' not in new_row_unscaled_data:
                        # 检查是否为月初（前5天）
                        is_month_start = 1 if next_timestamp.day <= 5 else 0
                        new_row_unscaled_data['is_month_start'] = is_month_start

                    if 'is_month_end' not in new_row_unscaled_data:
                        # 检查是否为月末（后5天）
                        days_in_month = pd.Timestamp(next_timestamp.year, next_timestamp.month, 1) + pd.offsets.MonthEnd(1)
                        days_in_month = days_in_month.day
                        is_month_end = 1 if (days_in_month - next_timestamp.day) <= 5 else 0
                        new_row_unscaled_data['is_month_end'] = is_month_end

                    logger.debug(f"    步骤 {step + 1}: 股票特有特征 - is_trading_day: {new_row_unscaled_data.get('is_trading_day')}, is_month_start: {new_row_unscaled_data.get('is_month_start')}, is_month_end: {new_row_unscaled_data.get('is_month_end')}")
                    # 计算股票派生特征
                    close_price = new_row_unscaled_data.get('close_price', np.nan)
                    open_price = new_row_unscaled_data.get('open_price', np.nan)
                    high_price = new_row_unscaled_data.get('high_price', np.nan)
                    low_price = new_row_unscaled_data.get('low_price', np.nan)
                    volume = new_row_unscaled_data.get('volume', np.nan)

                    # 计算价格变化
                    if not pd.isna(close_price):
                        last_close = history_df_unscaled_iter['close_price'].iloc[-1]
                        if not pd.isna(last_close) and last_close != 0:
                            price_change = close_price - last_close
                            price_change_pct = price_change / last_close
                            new_row_unscaled_data['price_change'] = price_change
                            new_row_unscaled_data['price_change_pct'] = price_change_pct
                            logger.debug(f"    步骤 {step + 1}: 计算 price_change: {price_change:.4f}, price_change_pct: {price_change_pct:.4f}")

                    # 计算价格波动性
                    if not pd.isna(high_price) and not pd.isna(low_price):
                        price_range = high_price - low_price
                        if not pd.isna(low_price) and low_price != 0:
                            price_range_pct = price_range / low_price
                            new_row_unscaled_data['price_range'] = price_range
                            new_row_unscaled_data['price_range_pct'] = price_range_pct
                            logger.debug(f"    步骤 {step + 1}: 计算 price_range: {price_range:.4f}, price_range_pct: {price_range_pct:.4f}")

                    # 计算成交量价格比
                    if not pd.isna(volume) and not pd.isna(close_price) and close_price != 0:
                        volume_price_ratio = volume / close_price
                        new_row_unscaled_data['volume_price_ratio'] = volume_price_ratio
                        logger.debug(f"    步骤 {step + 1}: 计算 volume_price_ratio: {volume_price_ratio:.4f}")

                elif CURRENT_DATA_TYPE == 'ENERGY':
                    # 计算能源派生特征
                    power_consumption = new_row_unscaled_data.get('power_consumption', np.nan)
                    temperature = new_row_unscaled_data.get('temperature', np.nan)

                    # 计算功耗变化
                    if not pd.isna(power_consumption) and 'power_change' in history_df_unscaled_iter.columns:
                        last_power = history_df_unscaled_iter['power_consumption'].iloc[-1]
                        if not pd.isna(last_power) and last_power != 0:
                            power_change = power_consumption - last_power
                            power_change_pct = power_change / last_power
                            new_row_unscaled_data['power_change'] = power_change
                            new_row_unscaled_data['power_change_pct'] = power_change_pct

                    # 计算功耗温度比
                    if not pd.isna(power_consumption) and not pd.isna(temperature) and 'power_temp_ratio' in history_df_unscaled_iter.columns:
                        power_temp_ratio = power_consumption / (temperature + 273.15)  # 转换为绝对温度
                        new_row_unscaled_data['power_temp_ratio'] = power_temp_ratio

                if step < 2:  # 保持这个日志，现在应该使用正确的变量了
                    # 安全地获取 heat_index 和 wind_chill 值，确保它们是数值类型才进行格式化
                    heat_index_val = new_row_unscaled_data.get('heat_index', np.nan)
                    wind_chill_val = new_row_unscaled_data.get('wind_chill', np.nan)
                    heat_index_str = f"{heat_index_val:.2f}" if isinstance(heat_index_val, (float, int, np.number)) and not pd.isna(heat_index_val) else "NaN"
                    wind_chill_str = f"{wind_chill_val:.2f}" if isinstance(wind_chill_val, (float, int, np.number)) and not pd.isna(wind_chill_val) else "NaN"
                    logger.debug(
                        f"    步骤 {step + 1}: 重新计算 heat_index: {heat_index_str}, wind_chill: {wind_chill_str}")

                #   e. 将新行添加到 history_df_unscaled_iter
                # 将新行添加到 history_df_unscaled_iter
                # 确保新行只包含 history_df_unscaled_iter 已有的列，顺序一致
                cols_for_new_row = history_df_unscaled_iter.columns
                data_for_new_series = {col: new_row_unscaled_data.get(col, np.nan) for col in cols_for_new_row}
                new_row_series = pd.Series(data_for_new_series, name=next_timestamp)
                history_df_unscaled_iter = pd.concat([history_df_unscaled_iter, new_row_series.to_frame().T])

                # 确保索引是 DatetimeIndex 并检查和尝试修复单调性
                history_df_unscaled_iter.index = pd.to_datetime(history_df_unscaled_iter.index)
                if not history_df_unscaled_iter.index.is_monotonic_increasing:
                    logger.warning(f"    步骤 {step + 1}: history_df_unscaled_iter 索引在 concat 后不单调递增!")
                    logger.debug(
                        f"      concat 前 history_df_unscaled_iter 最后索引: {history_df_unscaled_iter.index[-2] if len(history_df_unscaled_iter) >= 2 else 'N/A'}")
                    logger.debug(f"      新行索引 (next_timestamp): {next_timestamp}")
                    logger.debug(
                        f"      concat 后 history_df_unscaled_iter 最后5个索引: {history_df_unscaled_iter.index[-5:].tolist()}")

                    # 尝试通过排序和去重（保留最后一个）来修复，但这可能掩盖根本问题
                    history_df_unscaled_iter = history_df_unscaled_iter[
                        ~history_df_unscaled_iter.index.duplicated(keep='last')]
                    history_df_unscaled_iter = history_df_unscaled_iter.sort_index()

                    if not history_df_unscaled_iter.index.is_monotonic_increasing:
                        logger.error(f"    步骤 {step + 1}: 尝试修复后，索引仍然不单调递增！这是一个严重问题。停止递归。")
                        logger.error(
                            f"      修复后 history_df_unscaled_iter 最后5个索引: {history_df_unscaled_iter.index[-5:].tolist()}")
                        QMessageBox.critical(self, "递归错误",
                                             f"步骤 {step + 1}: 时间索引在合并新预测后出现严重错误（非单调），无法继续递归。请检查日志。")
                        break  # 中断 for 循环
                    else:
                        logger.info(f"    步骤 {step + 1}: 索引已通过去重和排序修复为单调递增。")

                # history_df_unscaled_iter.index = pd.to_datetime(history_df_unscaled_iter.index)  # 确保索引是 datetime

                #   f. 重新计算滚动特征 (在更新后的 history_df_unscaled_iter 的末尾计算新值)
                rolling_config = {'temperature': ['mean', 'std', 'max'], 'pressure': ['mean', 'std'],
                                  'humidity': ['mean']}
                win_roll = min(len(history_df_unscaled_iter), 37)  # 窗口不能超过可用数据
                temp_roll_df = history_df_unscaled_iter.iloc[-win_roll:]

                for base_col, stats in rolling_config.items():
                    if base_col not in temp_roll_df.columns: continue
                    for stat_fn in stats:
                        roll_col_name = f"{base_col}_6h_{stat_fn}"
                        if temp_roll_df[base_col].isna().all():
                            history_df_unscaled_iter.loc[next_timestamp, roll_col_name] = np.nan
                        else:
                            val = temp_roll_df[base_col].rolling('6h', min_periods=1).agg(stat_fn).iloc[-1]
                            history_df_unscaled_iter.loc[next_timestamp, roll_col_name] = np.clip(val, -1e6,
                                                                                                  1e6) if not pd.isna(
                                val) else np.nan  # 裁剪极端滚动值


                # 6. 构建下一步的 缩放后 输入特征向量
                last_gen_row_unscaled = history_df_unscaled_iter.iloc[-1]  # 这是包含了所有最新计算特征的 Pandas Series

                # 从 last_gen_row_unscaled 中提取 self.numeric_cols 对应的部分，准备进行批量缩放
                # 确保列的顺序与 self.scaler.fit 时一致
                # 根据不同的归一化器结构处理数值特征的缩放
                scaled_numeric_series = pd.Series(dtype=float)  # 创建一个空的Series用于存储所有缩放后的值

                # 检查是否使用新的scalers字典结构
                if hasattr(self, 'scalers') and self.scalers is not None:
                    # 处理minmax_scaler
                    if hasattr(self, 'minmax_cols') and self.minmax_cols and 'minmax_scaler' in self.scalers:
                        # 获取scaler期望的特征列表
                        expected_features = self.scalers['minmax_scaler'].feature_names_in_.tolist() if hasattr(self.scalers['minmax_scaler'], 'feature_names_in_') else self.minmax_cols

                        # 创建一个与scaler期望完全匹配的DataFrame
                        df_minmax_unscaled = pd.DataFrame(columns=expected_features)
                        df_minmax_unscaled.loc[0] = np.nan  # 添加一行，初始值为NaN

                        # 填充数据
                        for col in expected_features:
                            if col in last_gen_row_unscaled:
                                df_minmax_unscaled.loc[0, col] = last_gen_row_unscaled[col]
                            else:
                                # 如果是目标列但不在last_gen_row_unscaled中，使用默认值
                                if col == self.target_col:
                                    df_minmax_unscaled.loc[0, col] = 0.0
                                    logger.debug(f"目标列 {col} 不在历史数据中，使用默认值0.0")
                                else:
                                    # 对于其他缺失列，保持NaN
                                    logger.warning(f"特征列 {col} 在历史数据中缺失，使用NaN")

                        # 进行缩放
                        scaled_minmax_array = self.scalers['minmax_scaler'].transform(df_minmax_unscaled)

                        # 将缩放后的值添加到Series中
                        for i, col in enumerate(expected_features):
                            scaled_numeric_series[col] = scaled_minmax_array[0, i]

                    # 处理robust_scaler
                    if hasattr(self, 'robust_cols') and self.robust_cols and 'robust_scaler' in self.scalers:
                        # 获取scaler期望的特征列表
                        expected_features = self.scalers['robust_scaler'].feature_names_in_.tolist() if hasattr(self.scalers['robust_scaler'], 'feature_names_in_') else self.robust_cols

                        # 创建一个与scaler期望完全匹配的DataFrame
                        df_robust_unscaled = pd.DataFrame(columns=expected_features)
                        df_robust_unscaled.loc[0] = np.nan  # 添加一行，初始值为NaN

                        # 填充数据
                        for col in expected_features:
                            if col in last_gen_row_unscaled:
                                df_robust_unscaled.loc[0, col] = last_gen_row_unscaled[col]
                            else:
                                # 如果是目标列但不在last_gen_row_unscaled中，使用默认值
                                if col == self.target_col:
                                    df_robust_unscaled.loc[0, col] = 0.0
                                    logger.debug(f"目标列 {col} 不在历史数据中，使用默认值0.0")
                                else:
                                    # 对于其他缺失列，保持NaN
                                    logger.warning(f"特征列 {col} 在历史数据中缺失，使用NaN")

                        # 进行缩放
                        scaled_robust_array = self.scalers['robust_scaler'].transform(df_robust_unscaled)

                        # 将缩放后的值添加到Series中
                        for i, col in enumerate(expected_features):
                            scaled_numeric_series[col] = scaled_robust_array[0, i]

                # 兼容旧版本 - 使用单一scaler
                elif hasattr(self, 'scaler') and self.scaler is not None and hasattr(self, 'numeric_cols') and self.numeric_cols:
                    # 获取scaler期望的特征列表
                    expected_features = self.scaler.feature_names_in_.tolist() if hasattr(self.scaler, 'feature_names_in_') else self.numeric_cols

                    # 创建一个与scaler期望完全匹配的DataFrame
                    df_numeric_unscaled = pd.DataFrame(columns=expected_features)
                    df_numeric_unscaled.loc[0] = np.nan  # 添加一行，初始值为NaN

                    # 填充数据
                    for col in expected_features:
                        if col in last_gen_row_unscaled:
                            df_numeric_unscaled.loc[0, col] = last_gen_row_unscaled[col]
                        else:
                            # 如果是目标列但不在last_gen_row_unscaled中，使用默认值
                            if col == self.target_col:
                                df_numeric_unscaled.loc[0, col] = 0.0
                                logger.debug(f"目标列 {col} 不在历史数据中，使用默认值0.0")
                            else:
                                # 对于其他缺失列，保持NaN
                                logger.warning(f"特征列 {col} 在历史数据中缺失，使用NaN")

                    # 进行缩放
                    scaled_numeric_values_array = self.scaler.transform(df_numeric_unscaled)

                    # 将缩放后的值添加到Series中
                    for i, col in enumerate(expected_features):
                        scaled_numeric_series[col] = scaled_numeric_values_array[0, i]
                else:
                    logger.error("无法找到有效的归一化器结构，无法进行特征缩放。")
                    raise ValueError("缺少归一化器，无法进行递归预测。")

                next_feature_vector_scaled = []
                for f_name_model in self.feature_cols:  # self.feature_cols 是模型实际输入的特征列表
                    scaled_val_model = np.nan  # 初始化

                    if f_name_model in self.numeric_cols:
                        # 如果特征是参与 MinMax 归一化的数值特征，从批量缩放的结果中获取
                        scaled_val_model = scaled_numeric_series[f_name_model]
                    else:
                        # 如果特征不参与 MinMax 归一化 (例如已经是 sin/cos 编码，或者 'hour' 等)
                        # 直接从最新的未缩放行中获取其值 (这些值假定已是最终形式)
                        scaled_val_model = last_gen_row_unscaled.get(f_name_model, np.nan)

                    # 对任何可能产生的 NaN 值进行回退处理
                    if pd.isna(scaled_val_model):
                        logger.warning(f"特征 '{f_name_model}' 在处理/缩放后得到 NaN。尝试从上一步的缩放特征中回退。")
                        try:
                            # 从 self.feature_cols 中找到该特征的索引，以便从上一步的 current_scaled_feature_sequence 获取
                            idx_in_feature_cols = self.feature_cols.index(f_name_model)
                            # current_scaled_feature_sequence 存储的是上一时间步的完整缩放后特征向量
                            scaled_val_model = current_scaled_feature_sequence[-1][idx_in_feature_cols]
                            logger.info(f"  成功为 '{f_name_model}' 回退到上一步的缩放值: {scaled_val_model:.4f}")
                        except (
                        IndexError, ValueError, TypeError) as e_fallback:  # 添加 TypeError 以防 scaled_val_model 不是数字
                            logger.error(
                                f"  为 '{f_name_model}' 进行 NaN 回退失败: {e_fallback}。使用 0.0 代替。这可能影响预测准确性。")
                            scaled_val_model = 0.0  # 最后的防线

                    next_feature_vector_scaled.append(scaled_val_model)

                """if np.isnan(next_feature_vector_scaled).any():
                    nan_indices_final = np.where(np.isnan(next_feature_vector_scaled))[0]
                    nan_features_final = [self.feature_cols[i] for i in nan_indices_final]
                    logger.error(
                        f"步骤 {step + 1} next_feature_vector_scaled 最终包含 NaN! 特征: {nan_features_final}。可能导致后续预测失败。")
                    # 可以选择在这里 break，或者让模型尝试处理含NaN的输入（如果模型支持/有鲁棒性）
                    pass"""

                if step < 2 or (step + 1) % 72 == 0:
                    logger.debug(
                        f"  步骤 {step + 1} 下一特征向量 (送入下一轮，前5个): {[f'{x:.4f}' if isinstance(x, (float, np.number)) else x for x in next_feature_vector_scaled[:5]]}")

                # 7. 更新缩放后的序列历史 (用于模型的下一步输入)
                # 更新 current_scaled_feature_sequence
                current_scaled_feature_sequence.append(next_feature_vector_scaled)
                current_scaled_feature_sequence.pop(0)  # 保持长度

                current_timestamp = next_timestamp
                steps_completed += 1
                if (step + 1) % 24 == 0:
                    self.update_status_bar(f"递归预测: {step + 1}/{num_prediction_steps} 步完成...", 0)
                    QApplication.processEvents()

            logger.info(f"递归预测完成，共预测 {steps_completed} 步。")

            # --- 绘图结果 ---
            if predicted_values_unscaled:
                logger.info("绘制递归预测结果图...")
                self.data_fig.clf()
                ax_pred_recursive = self.data_fig.add_subplot(111)

                # +++ 动态历史数据显示 +++
                num_historical_to_plot = min(len(self.featured_df_before_norm), 7 * 24 * 6)  # 一周数据
                if num_historical_to_plot > 0:
                    historical_timestamps = self.featured_df_before_norm.index[-num_historical_to_plot:]
                    historical_actual_target = self.featured_df_before_norm[target_col].iloc[-num_historical_to_plot:]
                    ax_pred_recursive.plot(historical_timestamps, historical_actual_target,
                                           'b.-', markersize=3, linewidth=1, alpha=0.7,
                                           label=f'实际 {target_col}')

                ax_pred_recursive.plot(predicted_timestamps, predicted_values_unscaled, 'ro-',
                                       markersize=4, linewidth=1.5, label=f'预测 {target_col}')
                ax_pred_recursive.set_title(f'未来24小时 {target_col} 递归预测 (模型ID: {self.current_model_id})')
                ax_pred_recursive.grid(True, linestyle=':', alpha=0.7)
                ax_pred_recursive.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
                ax_pred_recursive.figure.autofmt_xdate()
                self.data_canvas.draw()
                self.update_status_bar(f"24小时递归预测完成 ({steps_completed} 步)", 5000)
            else:
                QMessageBox.information(self, "无预测", "未生成有效预测数据")

        except Exception as e:
            error_msg = f"递归预测失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            QMessageBox.critical(self, "错误", f"{error_msg}\n详见日志")
            self.update_status_bar("递归预测发生错误", 10000)

    def evaluate_current_model(self):
        """在验证集上评估当前加载或训练的模型，并显示结果。
        MSE (Mean Squared Error, 平均二乗誤差)
            计算: 计算每个预测点与实际值的差（误差），将这个差值平方，然后求所有点的平均值。
            含义: 它衡量的是预测误差的平方的平均值。由于误差是平方计算的，所以较大的误差会被放大，对模型的“惩罚”更重。
            特点: 值越小，表示模型的预测越接近实际值，性能越好。单位是目标变量单位的平方（例如，如果预测温度，单位是 (°C)²）。
            用途: 常作为模型训练过程中的损失函数，因为它在数学上性质较好（例如容易求导）。
        MAE (Mean Absolute Error, 平均絶対誤差)
            计算: 计算每个预测点与实际值的差，取其绝对值，然后求所有点的平均值。
            含义: 它衡量的是预测误差绝对值的平均大小。表示模型平均预测偏离实际值多少。
            特点: 值越小，性能越好。单位与目标变量相同（例如 °C），因此更直观地理解误差的大小。相比 MSE，它对个别异常大的误差（离群点）不太敏感。
            用途: 直观了解模型预测误差的平均幅度。
        RMSE (Root Mean Squared Error, 二乗平均平方根誤差)
            计算: 计算 MSE 后，再对其开平方根。
            含义: 也是衡量预测误差大小的指标。
            特点: 值越小，性能越好。单位与目标变量相同（例如 °C），便于解释。与 MAE 相比，RMSE 因为是基于 MSE 计算的，所以仍然会对较大的误差给予更多的权重。通常 RMSE >= MAE。
            用途: 当希望误差单位与原数据一致，同时又想强调较大误差时使用。
        R² (R-squared, 決定係数)
            计算: 它表示模型预测能够解释目标变量**变异性（方差）**的比例。计算公式相对复杂，但本质上是将模型的预测误差与“简单地使用目标变量平均值进行预测”的误差进行比较。
            含义: R² 的值通常在 0 到 1 之间（也可能为负）。
            接近 1: 表示模型能够很好地解释目标变量的变化，预测效果好。
            接近 0: 表示模型预测效果不比直接使用平均值好多少。
            负值: 表示模型预测效果比直接使用平均值还要差（这通常说明模型非常不拟合）。
            特点: 它提供了一个相对的性能度量，表示模型拟合数据的优良程度，而不是误差的绝对大小。
            用途: 评估模型的整体拟合优度，看模型在多大程度上抓住了数据的规律。
        """
        logger.info("正在评估当前模型...")
        # 前置检查
        if self.model is None:
            QMessageBox.warning(self, "无模型", "没有活动的模型可用于评估。")
            return
        if self.data is None or self.data.empty:
            QMessageBox.warning(self, "无数据", "缺少数据，无法进行评估。")
            return

        # 准备验证数据加载器
        # 基于当前数据状态重新创建验证加载器
        try:
            logger.info("正在准备用于评估的验证数据加载器...")
            target_col = self.target_col
            feature_cols = self.feature_cols
            seq_length = self.seq_length

            if not feature_cols: raise ValueError("特征列未设置。")

            X_full = self.data[feature_cols].values.astype(np.float32)
            y_full = self.data[target_col].values.astype(np.float32)

            num_samples_full = len(X_full) - seq_length
            if num_samples_full <= 0: raise ValueError("数据不足以创建序列。")

            X_seq_full = np.array([X_full[i : i + seq_length] for i in range(num_samples_full)])
            y_seq_full = np.array([y_full[i + seq_length] for i in range(num_samples_full)])
            if y_seq_full.ndim == 1: y_seq_full = y_seq_full[:, np.newaxis]

            # 使用与训练相同的分割逻辑来获取验证部分
            split_ratio = 0.8
            split_idx = int(split_ratio * num_samples_full)
            if split_idx >= num_samples_full: # 处理分割导致没有验证数据的情况
                 raise ValueError("数据分割导致验证集为空。")

            X_val = X_seq_full[split_idx:]
            y_val = y_seq_full[split_idx:]
            if X_val.shape[0] == 0: raise ValueError("分割后验证集为空。")

            X_val_tensor = torch.from_numpy(X_val)
            y_val_tensor = torch.from_numpy(y_val)
            val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
            val_loader = DataLoader(val_dataset, batch_size=64, shuffle=False) # 评估时使用更大的批次
            logger.info(f"验证加载器已创建，包含 {len(val_dataset)} 个样本。")

        except Exception as e:
            error_msg = f"准备评估数据时出错: {e}"
            logger.error(error_msg, exc_info=True)
            QMessageBox.critical(self, "数据错误", error_msg)
            return

        # 执行评估
        self.update_status_bar("正在评估模型...", 0)
        QApplication.processEvents()
        device = self.get_selected_device()
        criterion = nn.MSELoss() # 使用标准MSE进行评估显示

        try:
            avg_loss, targets_np, preds_np, extra_metrics = self.model.evaluate(
                val_loader, criterion, device=device, calculate_extra_metrics=True
            )

            logger.info("评估完成。")
            logger.info(f"  MSE: {avg_loss:.6f}")
            if extra_metrics:
                logger.info(f"  MAE: {extra_metrics.get('mae', 'N/A'):.6f}")
                logger.info(f"  RMSE: {extra_metrics.get('rmse', 'N/A'):.6f}")
                logger.info(f"  R2: {extra_metrics.get('r2', 'N/A'):.4f}")

            # 在评估选项卡中显示指标
            self.eval_label.setText(f"当前模型 ({self.model.original_model_type}, ID: {self.current_model_id or 'N/A'}) 评估结果 (验证集):")
            self.eval_table.setRowCount(0) # 清除之前的结果

            metrics_to_display = {
                "MSE": f"{avg_loss:.6f}",
                "MAE": f"{extra_metrics.get('mae', 'N/A'):.6f}" if extra_metrics and 'mae' in extra_metrics else "N/A",
                "RMSE": f"{extra_metrics.get('rmse', 'N/A'):.6f}" if extra_metrics and 'rmse' in extra_metrics else "N/A",
                "R-squared (R²)": f"{extra_metrics.get('r2', 'N/A'):.4f}" if extra_metrics and 'r2' in extra_metrics else "N/A"
            }
            if extra_metrics and extra_metrics.get('error'):
                 metrics_to_display["指标错误"] = str(extra_metrics['error'])

            self.eval_table.setRowCount(len(metrics_to_display))
            for row, (name, value) in enumerate(metrics_to_display.items()):
                 self.eval_table.setItem(row, 0, QTableWidgetItem(name))
                 self.eval_table.setItem(row, 1, QTableWidgetItem(str(value)))

            self.tab_widget.setCurrentWidget(self.tab_evaluation) # 切换到评估选项卡
            self.update_status_bar("模型评估完成。", 5000)

        except Exception as e:
            error_msg = f"模型评估失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            QMessageBox.critical(self, "评估错误", error_msg)
            self.update_status_bar(f"错误: {error_msg[:100]}...", 10000)
            self.eval_label.setText("模型评估失败。")
            self.eval_table.setRowCount(0)


    def load_model_comparison_data(self):
        """从数据库加载所有模型信息并显示在表格中 (增强版)"""
        logger.info("正在从数据库刷新模型比较表...")
        self.update_status_bar("正在从数据库加载模型列表...", 0)
        QApplication.processEvents()

        try:
             models = self.db_manager.get_all_models() # 这获取基本信息
             # 也许以后可以增强get_all_models以直接包含摘要信息？
             # 目前，基本信息对于主表显示已足够。

             # 清除之前的选择和表格内容
             self.model_table.clearSelection()
             self.model_table.setRowCount(0) # 在设置列数之前清除行
             self.model_table.setColumnCount(0)

             if not models:
                 logger.info("数据库中未找到模型。")
                 headers = ['ID', '类型', '完成时间', '运行轮次', '序列长度', '最终验证损失', '输入维度', '配置摘要']
                 self.model_table.setColumnCount(len(headers))
                 self.model_table.setHorizontalHeaderLabels(headers)
                 self.update_status_bar("数据库中没有已保存的模型。", 3000)
                 self.on_model_table_selection_changed() # 更新按钮状态
                 return

             # 设置表格表头
             headers = ['ID', '类型', '完成时间', '运行轮次', '序列长度', '最终验证损失', '输入维度', '配置摘要']
             self.model_table.setColumnCount(len(headers))
             self.model_table.setHorizontalHeaderLabels(headers)
             self.model_table.setRowCount(len(models))

             # 填充表格行
             for row_idx, model_info in enumerate(models):
                 # ID（确保可以按数字排序）
                 id_item = QTableWidgetItem()
                 id_val = int(model_info['id'])
                 id_item.setData(Qt.ItemDataRole.EditRole, id_val)
                 id_item.setText(str(id_val))
                 id_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                 self.model_table.setItem(row_idx, 0, id_item)

                 # 类型
                 self.model_table.setItem(row_idx, 1, QTableWidgetItem(model_info['model_type']))

                 # 时间戳（保持为字符串用于显示/排序）
                 train_time_str = str(model_info['train_end_time'])[:19] # YYYY-MM-DD HH:MM:SS
                 time_item = QTableWidgetItem(train_time_str)
                 self.model_table.setItem(row_idx, 2, time_item)

                 # 运行轮次（数字）
                 epoch_item = QTableWidgetItem()
                 epoch_val = int(model_info['epochs_run'])
                 epoch_item.setData(Qt.ItemDataRole.EditRole, epoch_val)
                 epoch_item.setText(str(epoch_val))
                 epoch_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                 self.model_table.setItem(row_idx, 3, epoch_item)

                 # 序列长度（数字）
                 seq_item = QTableWidgetItem()
                 seq_val = int(model_info['seq_length'])
                 seq_item.setData(Qt.ItemDataRole.EditRole, seq_val)
                 seq_item.setText(str(seq_val))
                 seq_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                 self.model_table.setItem(row_idx, 4, seq_item)

                 # 最终验证损失（数字）
                 loss_item = QTableWidgetItem()
                 loss_val = float(model_info['final_val_loss'])
                 loss_item.setData(Qt.ItemDataRole.EditRole, loss_val)
                 loss_item.setText(f"{loss_val:.6f}")
                 loss_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                 self.model_table.setItem(row_idx, 5, loss_item)

                 # 输入维度（数字）
                 dim_item = QTableWidgetItem()
                 dim_val = int(model_info['input_dim'])
                 dim_item.setData(Qt.ItemDataRole.EditRole, dim_val)
                 dim_item.setText(str(dim_val))
                 dim_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                 self.model_table.setItem(row_idx, 6, dim_item)


                 # 配置摘要
                 config_dict = model_info.get('config', {})
                 config_str_short = self._get_short_config_display(config_dict, model_info.get('model_type'))
                 config_item = QTableWidgetItem(config_str_short)
                 # 工具提示中的完整配置
                 try:
                      full_config_str = json.dumps(config_dict, indent=2, ensure_ascii=False, sort_keys=True)
                      config_item.setToolTip(f"<pre>{full_config_str}</pre>") # 使用预格式化标签以获得更好的显示效果
                 except TypeError:
                      config_item.setToolTip("无法序列化配置")
                 self.model_table.setItem(row_idx, 7, config_item)


             # 填充后调整列宽
             self.model_table.resizeColumnsToContents()
             # 允许配置列拉伸
             header = self.model_table.horizontalHeader()
             header.setSectionResizeMode(7, QHeaderView.ResizeMode.Stretch)
             # 可能为ID设置最小宽度？
             header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
             header.setMinimumSectionSize(40)


             logger.info(f"模型比较表已刷新，包含 {len(models)} 个条目。")
             self.update_status_bar("模型列表加载完成。", 3000)

        except Exception as e:
             error_msg = f"加载模型列表失败: {e}"
             logger.error(error_msg, exc_info=True)
             QMessageBox.critical(self, "数据库错误", error_msg)
             self.update_status_bar("错误：加载模型列表失败。", 10000)

        finally:
             # 无论成功/失败都更新按钮状态
             self.on_model_table_selection_changed()


    def _get_short_config_display(self, config_dict, model_type):
        """辅助函数，生成用于表格显示的简化配置字符串。"""
        if not isinstance(config_dict, dict): return "{}"
        # 定义与每种类型的简短显示相关的键
        common_keys = ['dropout']
        rnn_keys = ['hidden_dim', 'num_layers']
        trans_keys = ['hidden_dim', 'num_layers', 'nhead']
        tcn_keys = ['tcn_num_channels', 'tcn_kernel_size']

        keys_to_show = common_keys
        if model_type == 'LSTM' or model_type == 'GRU':
             keys_to_show.extend(rnn_keys)
        elif model_type == 'Transformer':
             keys_to_show.extend(trans_keys)
        elif model_type == 'TCN':
             keys_to_show.extend(tcn_keys)

        display_dict = {}
        for key in keys_to_show:
            if key in config_dict:
                 value = config_dict[key]
                 # TCN通道列表的特殊格式化
                 if key == 'tcn_num_channels' and isinstance(value, list):
                      display_dict[key] = f"[{len(value)}] {str(value[:2])}{'...' if len(value)>2 else ''}"
                 # 为极端简短而缩写键？例如，h=, l=, d=, nh=, tc=, tk=
                 # short_key = {'hidden_dim':'hd', 'num_layers':'l', 'dropout':'dr', 'nhead':'nh', 'tcn_num_channels':'tc', 'tcn_kernel_size':'tk'}.get(key, key)
                 # display_dict[short_key] = value
                 else:
                      display_dict[key] = value # 使用原始键名

        # 转换为字符串表示（例如，"dr=0.1, hd=64, l=2"）
        return ', '.join(f"{k}={v}" for k, v in display_dict.items()) if display_dict else "{}"


    def on_model_table_selection_changed(self):
        """处理模型表格选中行变化，更新相关按钮状态。"""
        selected_indexes = self.model_table.selectionModel().selectedRows() # Get QModelIndex list for selected rows
        num_selected = len(selected_indexes)

        can_load = num_selected == 1
        can_delete = num_selected >= 1
        can_view_single_log = num_selected == 1
        can_compare = num_selected >= 2

        self.btn_load_selected_model.setEnabled(can_load)
        self.btn_delete_selected_model.setEnabled(can_delete)
        self.btn_view_logs.setEnabled(can_view_single_log)
        self.btn_compare_logs.setEnabled(can_compare)
        self.btn_compare_metrics.setEnabled(can_compare)


    def get_selected_model_ids_from_table(self):
        """从模型表格中获取选中行的模型 ID 列表。"""
        selected_ids = []
        selected_rows = self.model_table.selectionModel().selectedRows()
        if not selected_rows:
            return selected_ids

        id_column_index = 0 # Assuming ID is the first column
        for row_model_index in selected_rows:
            item = self.model_table.item(row_model_index.row(), id_column_index)
            if item:
                try:
                    # Use the stored numerical data for robustness if available
                    id_value = item.data(Qt.ItemDataRole.EditRole)
                    if id_value is not None:
                         selected_ids.append(int(id_value))
                    else: # Fallback to text if data role is missing
                         selected_ids.append(int(item.text()))
                except (ValueError, TypeError):
                    logger.warning(f"Could not parse model ID from table cell: row {row_model_index.row()}, text '{item.text()}'")
            else:
                logger.warning(f"Could not get item for model ID from table row {row_model_index.row()}")

        return selected_ids


    def compare_selected_model_metrics(self):
        """获取选中模型的摘要指标并在对话框中显示对比表格。"""
        selected_model_ids = self.get_selected_model_ids_from_table()

        if len(selected_model_ids) < 2:
            QMessageBox.warning(self, "选择不足", "请在表格中至少选择两个模型进行指标对比。")
            return

        logger.info(f"正在获取模型ID的比较数据: {selected_model_ids}")
        self.update_status_bar("正在查询模型对比数据...", 0)
        QApplication.processEvents()

        try:
            # 使用增强的数据库方法
            comparison_data = self.db_manager.get_comparison_data_for_models(selected_model_ids)

            if not comparison_data:
                logger.info("未找到所选模型的比较数据。")
                QMessageBox.information(self, "无数据", "未能查询到选中模型的对比数据。\n（可能是摘要信息尚未完全生成，或者模型已被删除）")
                self.update_status_bar("查询完成，无对比数据。", 3000)
                return

            logger.info(f"成功获取了 {len(comparison_data)} 个模型的比较数据。")
            # 显示比较对话框
            dialog = MetricComparisonDialog(comparison_data, self)
            dialog.exec() # 显示为模态对话框
            self.update_status_bar("模型指标对比已显示。", 3000)

        except Exception as e:
            error_msg = f"获取模型对比数据时出错: {e}"
            logger.error(error_msg, exc_info=True)
            QMessageBox.critical(self, "查询错误", error_msg)
            self.update_status_bar("错误：查询对比数据失败。", 10000)


    def compare_selected_model_logs(self):
        """获取多个选中模型的训练日志并在训练监控图表中对比显示。"""
        selected_model_ids = self.get_selected_model_ids_from_table()

        if len(selected_model_ids) < 2:
            QMessageBox.warning(self, "选择不足", "请在表格中至少选择两个模型进行日志对比。")
            return

        logger.info(f"正在获取模型ID的训练日志进行比较: {selected_model_ids}")
        self.update_status_bar("正在查询训练日志...", 0)
        QApplication.processEvents()

        model_data_to_plot = [] # 存储 (model_id, model_info_str, logs)
        found_logs_count = 0
        has_error = False

        # 直接从表格获取模型信息字符串以保持一致性
        selected_rows_indices = [idx.row() for idx in self.model_table.selectionModel().selectedRows()]

        for row_idx in selected_rows_indices:
             try:
                 model_id_item = self.model_table.item(row_idx, 0)
                 model_type_item = self.model_table.item(row_idx, 1)
                 loss_item = self.model_table.item(row_idx, 5) # Final loss

                 if not model_id_item or not model_type_item:
                     logger.warning(f"表格行 {row_idx} 中缺少ID或类型项。跳过。")
                     continue

                 model_id = int(model_id_item.data(Qt.ItemDataRole.EditRole) or int(model_id_item.text())) # 优先使用数值数据
                 model_type = model_type_item.text()
                 final_loss = float(loss_item.data(Qt.ItemDataRole.EditRole) or float(loss_item.text())) if loss_item else None
                 final_loss_str = f"{final_loss:.4f}" if final_loss is not None else "N/A"
                 model_info_str = f"ID {model_id} ({model_type}, L:{final_loss_str})" # 更短的图例

                 logger.debug(f"正在查询模型ID {model_id} 的日志...")
                 logs = self.db_manager.get_training_logs(model_id)
                 if logs:
                     model_data_to_plot.append({'id': model_id, 'label': model_info_str, 'logs': logs})
                     found_logs_count += 1
                     logger.debug(f"   找到 {len(logs)} 个日志条目。")
                 else:
                     logger.warning(f"   未找到模型ID {model_id} 的训练日志。")

             except Exception as e:
                  logger.error(f"处理模型行 {row_idx} 进行日志比较时出错: {e}", exc_info=True)
                  has_error = True
                  # 继续处理其他模型

        if has_error:
             QMessageBox.warning(self, "部分错误", "处理部分选中模型时发生错误，请检查日志。")

        if found_logs_count == 0:
            QMessageBox.information(self, "无日志", "所有选中的模型均未找到训练日志记录。")
            self.loss_ax.clear(); self.lr_ax.clear(); self.lr_ax.get_yaxis().set_visible(False)
            self.loss_ax.set_title('无可用日志进行对比')
            self.loss_ax.grid(True)
            self.train_canvas.draw()
            self.update_status_bar("查询完成，无训练日志。", 3000)
            return

        # 绘制日志
        self.loss_ax.clear()
        if self.lr_ax: self.lr_ax.clear(); self.lr_ax.get_yaxis().set_visible(False) # 同时清除LR轴
        all_min_loss = float('inf')
        all_max_loss = float('-inf')

        logger.info(f"正在绘制 {found_logs_count} 个模型的比较日志。")
        for i, data in enumerate(model_data_to_plot):
            logs = data['logs']
            epochs = [log[0] for log in logs]
            train_losses = [log[1] for log in logs]
            val_losses = [log[2] for log in logs]

            if not epochs: continue # 如果日志在检查后仍然为空，则跳过

            # 跟踪整体损失范围
            current_min = min(train_losses + val_losses)
            current_max = max(train_losses + val_losses)
            all_min_loss = min(all_min_loss, current_min)
            all_max_loss = max(all_max_loss, current_max)

            color = self.plot_colors[i % len(self.plot_colors)] # 循环使用颜色

            # 绘制训练和验证损失
            self.loss_ax.plot(epochs, train_losses, color=color, linestyle='-', marker='None', markersize=3, alpha=0.8, label=f'{data["label"]} 训练')
            self.loss_ax.plot(epochs, val_losses, color=color, linestyle='--', marker='None', markersize=3, alpha=0.8, label=f'{data["label"]} 验证')

        # 配置绘图轴和图例
        self.loss_ax.set_title(f'对比 {found_logs_count} 个模型的训练日志')
        self.loss_ax.set_xlabel('训练轮次')
        self.loss_ax.set_ylabel('损失值 (MSE)')
        self.loss_ax.legend(fontsize='x-small', loc='best', ncol=max(1, found_logs_count // 4)) # 调整图例布局
        self.loss_ax.grid(True, linestyle=':', alpha=0.7)

        # 根据观察到的范围设置Y轴限制
        if all_min_loss != float('inf'):
            padding = (all_max_loss - all_min_loss) * 0.05 if all_max_loss > all_min_loss else 0.01
            y_lower = max(0, all_min_loss - padding) if all_min_loss >= 0 else all_min_loss - padding
            y_upper = all_max_loss + padding
            # 添加限制的合理性检查
            if y_upper > y_lower:
                 self.loss_ax.set_ylim(y_lower, y_upper)
            else: # 如果计算异常则回退
                 self.loss_ax.autoscale_view(True, False, True)
        else: # 如果没有有效损失则回退
             self.loss_ax.autoscale_view(True, False, True)


        # 绘制并切换选项卡
        self.train_canvas.draw()
        self.tab_widget.setCurrentWidget(self.tab_train_monitor)
        self.update_status_bar(f"已显示 {found_logs_count} 个模型的日志对比。", 3000)


    def view_selected_model_logs(self):
        """获取单个选中模型的训练日志并在训练监控图中显示。"""
        selected_model_ids = self.get_selected_model_ids_from_table()

        if len(selected_model_ids) != 1:
            QMessageBox.warning(self, "选择错误", "请在表格中只选择一个模型来查看其日志。")
            return

        model_id = selected_model_ids[0]
        # 从表格获取模型类型用于标题
        selected_row_index = self.model_table.selectionModel().selectedRows()[0].row()
        model_type = self.model_table.item(selected_row_index, 1).text() if self.model_table.item(selected_row_index, 1) else "未知类型"

        logger.info(f"正在获取单个模型ID的训练日志: {model_id}")
        self.update_status_bar(f"正在查询模型 {model_id} 的训练日志...", 0)
        QApplication.processEvents()

        try:
            logs = self.db_manager.get_training_logs(model_id)

            if not logs:
                logger.info(f"未找到模型ID {model_id} 的训练日志。")
                QMessageBox.information(self, "无日志", f"模型 ID {model_id} 没有找到训练日志记录。")
                self.loss_ax.clear(); self.lr_ax.clear(); self.lr_ax.get_yaxis().set_visible(False)
                self.loss_ax.set_title(f'模型 {model_id} ({model_type}) 无训练日志')
                self.loss_ax.grid(True)
                self.train_canvas.draw()
                self.update_status_bar(f"模型 {model_id} 无训练日志。", 3000)
                return

            # 绘制日志
            epochs = [log[0] for log in logs]
            train_losses = [log[1] for log in logs]
            val_losses = [log[2] for log in logs]

            self.loss_ax.clear()
            if self.lr_ax: self.lr_ax.clear(); self.lr_ax.get_yaxis().set_visible(False) # 隐藏学习率轴

            self.train_loss_line, = self.loss_ax.plot(epochs, train_losses, 'b-', marker='.', label='训练损失 (MSE)')
            self.val_loss_line, = self.loss_ax.plot(epochs, val_losses, 'r-', marker='.', label='验证损失 (MSE)')

            self.loss_ax.set_title(f'模型 {model_id} ({model_type}) 训练日志')
            self.loss_ax.set_xlabel('训练轮次')
            self.loss_ax.set_ylabel('损失值 (MSE)')
            self.loss_ax.legend(loc='best')
            self.loss_ax.grid(True, linestyle=':', alpha=0.7)

            # 自动调整Y轴限制
            self.loss_ax.relim()
            self.loss_ax.autoscale_view(True, True, True) # 自动缩放两个轴

            self.train_canvas.draw()
            self.tab_widget.setCurrentWidget(self.tab_train_monitor)
            self.update_status_bar(f"已显示模型 {model_id} 的训练日志。", 3000)

        except Exception as e:
            error_msg = f"获取模型 {model_id} 日志时出错: {e}"
            logger.error(error_msg, exc_info=True)
            QMessageBox.critical(self, "查询错误", error_msg)
            self.update_status_bar(f"错误：查询模型 {model_id} 日志失败。", 10000)


    def load_selected_model_from_table(self):
        """从表格中加载选中的模型到主界面以进行预测或评估。"""
        selected_model_ids = self.get_selected_model_ids_from_table()

        if len(selected_model_ids) != 1:
            QMessageBox.warning(self, "选择错误", "请在表格中只选择一个模型进行加载。")
            return

        model_id_to_load = selected_model_ids[0]
        logger.info(f"尝试从数据库加载模型ID {model_id_to_load}...")
        self.update_status_bar(f"正在加载模型 {model_id_to_load}...", 0)
        QApplication.processEvents()

        # 我们需要一种加载模型state_dict的方法
        # 选项1：在数据库中存储模型state_dict（不推荐用于大型模型 - BLOB）
        # 选项2：在数据库中存储已保存模型的文件路径（需要保存.pth文件）
        # 选项3：使用保存的配置重新训练模型（慢，不实用）

        # 假设选项2：模型状态保存到文件
        # 我们需要将model_id与文件路径关联
        # 假设命名约定，例如 saved_models/model_{id}.pth
        model_save_dir = "saved_models"
        model_file_path = os.path.join(model_save_dir, f"model_{model_id_to_load}.pth")

        if not os.path.exists(model_file_path):
             QMessageBox.critical(self, "加载失败", f"找不到模型文件:\n{model_file_path}\n\n请确保模型训练时已保存，并且路径正确。")
             logger.error(f"模型ID {model_id_to_load} 的文件未在 {model_file_path} 找到")
             self.update_status_bar(f"错误：找不到模型 {model_id_to_load} 的文件。", 5000)
             return

        # 加载期间禁用UI
        self.set_controls_enabled(False)
        try:
            device = self.get_selected_device()
            # 使用TimeSeriesModel类方法加载
            loaded_model = TimeSeriesModel.load_model_from_file(model_file_path, device=device)

            # 使用加载的模型更新应用程序状态
            self.model = loaded_model
            self.current_model_id = model_id_to_load
            self.current_model_config = loaded_model.config # 从加载的模型获取配置
            # 更新UI控件以反映加载模型的参数
            self.model_selector.setCurrentText(loaded_model.original_model_type)
            self.seq_length_selector.setValue(loaded_model.config.get('seq_length', self.seq_length)) # 序列长度可能不在配置中？应该被保存。
            # 根据加载的配置更新其他超参数
            # ... (这部分需要仔细地将配置映射回UI小部件) ...
            self._update_ui_from_config(self.current_model_config)

            logger.info(f"成功将模型ID {model_id_to_load} 加载到应用程序中。")
            self.update_status_bar(f"模型 ID {model_id_to_load} ({loaded_model.original_model_type}) 已加载。", 5000)

            # 更新模型目标标签
            self.model_target_label.setText(f"当前模型: 预测 {self.target_col}")
            self.model_target_label.setStyleSheet("font-weight: bold; color: #008800;")

            # 检查模型的目标列是否与当前选择的目标列匹配
            if hasattr(loaded_model, 'target_col') and loaded_model.target_col != self.target_col:
                logger.warning(f"加载的模型目标列 ({loaded_model.target_col}) 与当前选择的目标列 ({self.target_col}) 不匹配！")
                QMessageBox.warning(self, "目标列不匹配",
                                   f"加载的模型训练时使用的目标列 ({loaded_model.target_col}) 与当前选择的目标列 ({self.target_col}) 不匹配！\n\n"
                                   f"请将目标列切换回 {loaded_model.target_col} 以使用此模型进行预测。")

                # 禁用预测按钮
                self.btn_predict_single.setEnabled(False)
                self.btn_predict_24h.setEnabled(False)
                self.btn_evaluate_loaded.setEnabled(False)
                self.oracle_mode_checkbox.setEnabled(False)

                # 更新模型目标标签，显示不匹配警告
                self.model_target_label.setText(f"目标列不匹配! 模型预测: {loaded_model.target_col}")
                self.model_target_label.setStyleSheet("font-weight: bold; color: #cc0000;")

                return

            # 如果数据也已加载，则启用相关按钮
            if self.data is not None:
                self.btn_predict_single.setEnabled(True)
                self.btn_predict_24h.setEnabled(True)
                self.btn_evaluate_loaded.setEnabled(True)
                self.oracle_mode_checkbox.setEnabled(True)
            else:
                 logger.warning("模型已加载，但没有数据。预测/评估已禁用。")

            # 可选：自动显示加载模型的评估结果
            # if self.data is not None: self.evaluate_current_model()

        except Exception as e:
             error_msg = f"加载模型 ID {model_id_to_load} 失败: {e}"
             logger.error(error_msg, exc_info=True)
             QMessageBox.critical(self, "加载错误", error_msg)
             self.update_status_bar(f"错误：加载模型 {model_id_to_load} 失败。", 10000)
             # 如果加载失败则重置模型状态
             self.model = None
             self.current_model_id = None
             self.current_model_config = {}
             self.btn_predict_single.setEnabled(False)
             self.btn_predict_24h.setEnabled(False)
             self.btn_evaluate_loaded.setEnabled(False)
        finally:
             # 重新启用UI
             self.set_controls_enabled(True)
             # 根据加载是否成功和数据是否存在调整按钮状态
             can_predict_eval = self.model is not None and self.data is not None
             self.btn_predict_single.setEnabled(can_predict_eval)
             self.btn_predict_24h.setEnabled(can_predict_eval)
             self.btn_evaluate_loaded.setEnabled(can_predict_eval)


    def _update_ui_from_config(self, config):
        """辅助函数，根据加载的配置字典更新UI超参数小部件。"""
        logger.debug(f"从加载的配置更新UI控件: {config}")
        # 更新通用控件
        self.lr_double_spin.setValue(config.get('lr', self.learning_rate)) # 配置中需要学习率！在保存时添加？
        self.dropout_double_spin.setValue(config.get('dropout', 0.1))

        # 更新模型特定控件
        model_type = self.model_selector.currentText() # 设置后使用当前选择器值
        if model_type in ['LSTM', 'GRU']:
             self.hidden_dim_spin.setValue(config.get('hidden_dim', 64))
             self.num_layers_spin.setValue(config.get('num_layers', 2))
        elif model_type == 'Transformer':
             self.hidden_dim_spin.setValue(config.get('hidden_dim', 64))
             self.num_layers_spin.setValue(config.get('num_layers', 2))
             self.nhead_spin.setValue(config.get('nhead', 4))
        elif model_type == 'TCN':
             channels = config.get('tcn_num_channels', [32, 64])
             self.tcn_channels_edit.setText(','.join(map(str, channels)))
             self.tcn_kernel_spin.setValue(config.get('tcn_kernel_size', 5))

        # 确保正确的小部件可见
        self.update_hyperparameter_ui()

    def delete_selected_model_from_table(self):
        """从数据库和（可选地）文件系统中删除选中的模型记录。"""
        selected_model_ids = self.get_selected_model_ids_from_table()

        if not selected_model_ids:
            QMessageBox.warning(self, "未选择", "请在表格中至少选择一个模型进行删除。")
            return

        confirm_msg = (f"确实要永久删除选中的 {len(selected_model_ids)} 个模型记录吗？\n"
                       f"模型 ID: {', '.join(map(str, selected_model_ids))}\n\n"
                       "警告：此操作会从数据库中删除模型信息、训练日志和摘要。\n"
                       "（模型权重文件 .pth 不会自动删除，需要手动清理 saved_models 文件夹）")

        reply = QMessageBox.question(self, '确认删除', confirm_msg,
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.Cancel,
                                     QMessageBox.StandardButton.Cancel)

        if reply == QMessageBox.StandardButton.Cancel:
            logger.info("用户取消了模型删除。")
            return

        logger.info(f"用户确认删除模型ID: {selected_model_ids}")
        self.update_status_bar(f"正在删除模型 {', '.join(map(str, selected_model_ids))}...", 0)
        QApplication.processEvents()

        deleted_count = 0
        errors = []

        # 数据库删除逻辑
        # 需要DBManager中的按ID删除方法
        # CASCADE约束应该处理相关的日志/摘要/预测
        # 假设db_manager.delete_model(model_id)存在

        if not hasattr(self.db_manager, 'delete_model'):
             QMessageBox.critical(self, "功能缺失", "DatabaseManager 中缺少 delete_model 方法。无法删除。")
             logger.error("未找到db_manager.delete_model方法。")
             self.update_status_bar("错误：删除功能未实现。", 5000)
             return # 在此停止

        for model_id in selected_model_ids:
            try:
                logger.info(f"从数据库删除模型ID {model_id}...")
                success = self.db_manager.delete_model(model_id) # 假设的方法
                if success:
                    logger.info(f"   成功从数据库删除模型ID {model_id}。")
                    deleted_count += 1
                    # 可选：删除对应的.pth文件
                    # model_save_dir = "saved_models"
                    # model_file_path = os.path.join(model_save_dir, f"model_{model_id}.pth")
                    # if os.path.exists(model_file_path):
                    #     try:
                    #         os.remove(model_file_path)
                    #         logger.info(f"   也删除了模型文件: {model_file_path}")
                    #     except OSError as file_err:
                    #         logger.warning(f"   无法删除模型文件 {model_file_path}: {file_err}")
                    #         errors.append(f"文件删除失败 (ID {model_id}): {file_err}")
                else:
                    logger.warning(f"   数据库删除报告模型ID {model_id} 失败。")
                    # 假设失败意味着它没有被删除，可能已经消失？
                    # 除非确认可靠，否则不增加deleted_count
                    errors.append(f"数据库删除失败 (ID {model_id})")

            except Exception as e:
                error_msg = f"删除模型 ID {model_id}"

def calculate_time_features_for_timestamp(timestamp, data_type='WEATHER'):
    """
    为单个时间戳计算增强型时间特征，支持不同类型的时序数据

    参数:
        timestamp: 时间戳
        data_type (str): 数据类型，可选值: 'WEATHER', 'STOCK', 'ENERGY', 'CUSTOM'

    返回:
        dict: 包含所有时间特征的字典
    """
    # 确保与data.py中的create_time_series_features函数保持一致
    # 将timestamp转换为pandas Timestamp对象
    if not isinstance(timestamp, pd.Timestamp):
        timestamp = pd.Timestamp(timestamp)

    # 基础时间特征
    features = {}

    # 小时特征 - 使用周期性编码以捕捉一天内的周期性
    hour = timestamp.hour
    hour_sin = math.sin(hour * 2 * math.pi / 24)
    hour_cos = math.cos(hour * 2 * math.pi / 24)
    features['hour'] = hour
    features['hour_sin'] = hour_sin
    features['hour_cos'] = hour_cos

    # 日期特征 - 年内的周期性
    dayofyear = timestamp.dayofyear
    day_sin = math.sin(dayofyear * 2 * math.pi / 365)
    day_cos = math.cos(dayofyear * 2 * math.pi / 365)
    features['day_sin'] = day_sin
    features['day_cos'] = day_cos

    # 星期特征 - 周内的周期性
    weekday = timestamp.weekday()
    weekday_sin = math.sin((weekday + 1) * 2 * math.pi / 7)
    weekday_cos = math.cos((weekday + 1) * 2 * math.pi / 7)
    features['weekday'] = weekday
    features['weekday_sin'] = weekday_sin
    features['weekday_cos'] = weekday_cos

    # 月份特征 - 年内的月度周期性
    month = timestamp.month
    month_sin = math.sin(month * 2 * math.pi / 12)
    month_cos = math.cos(month * 2 * math.pi / 12)
    features['month'] = month
    features['month_sin'] = month_sin
    features['month_cos'] = month_cos

    # 数据类型特定特征
    # 对于股票数据，添加交易日特征
    if data_type == 'STOCK':
        # 是否为交易日（周一至周五）
        is_trading_day = 1 if weekday < 5 else 0
        features['is_trading_day'] = is_trading_day

        # 是否为月初（前5天）
        is_month_start = 1 if timestamp.day <= 5 else 0
        features['is_month_start'] = is_month_start

        # 是否为月末（后5天）
        days_in_month = pd.Timestamp(timestamp.year, timestamp.month, 1) + pd.offsets.MonthEnd(1)
        days_in_month = days_in_month.day
        is_month_end = 1 if (days_in_month - timestamp.day) <= 5 else 0
        features['is_month_end'] = is_month_end

    # 对于能源数据，添加季节特征
    if data_type == 'ENERGY':
        # 季节（北半球）
        # 春季: 3-5月, 夏季: 6-8月, 秋季: 9-11月, 冬季: 12-2月
        season_map = {1: 'winter', 2: 'winter', 3: 'spring', 4: 'spring', 5: 'spring',
                      6: 'summer', 7: 'summer', 8: 'summer', 9: 'fall', 10: 'fall',
                      11: 'fall', 12: 'winter'}
        season = season_map[month]

        # 使用独热编码表示季节
        features['season_spring'] = 1 if season == 'spring' else 0
        features['season_summer'] = 1 if season == 'summer' else 0
        features['season_fall'] = 1 if season == 'fall' else 0
        features['season_winter'] = 1 if season == 'winter' else 0

        # 是否为工作日
        is_weekday = 1 if weekday < 5 else 0
        features['is_weekday'] = is_weekday

    return features

if __name__ == "__main__":
    import sys  # 确保导入 sys
    import os   # 确保导入 os
    import argparse  # 导入参数解析器

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='时序分析与预测平台')
    parser.add_argument('--auto-train', action='store_true', help='自动开始训练')
    args = parser.parse_args()

    # 创建虚拟数据文件
    # 创建气象数据
    if not os.path.exists("weather.csv"):
        print("创建虚拟 weather.csv 文件...")
        # 增加 periods 和调整频率以覆盖更长时间
        dummy_data = {
            'date': pd.to_datetime(pd.date_range(start='2020-01-01', periods=5000, freq='10min')),  # 增加到 5000 个点
            'p (mbar)': np.random.rand(5000) * 50 + 990,
            'T (degC)': np.sin(np.linspace(0, 100, 5000) / 10) * 10 + 15 + np.random.randn(5000),
            'rh (%)': np.random.rand(5000) * 50 + 40,
            'wv (m/s)': np.abs(np.sin(np.linspace(0, 200, 5000) / 5) * 3 + 1.5 + np.random.randn(5000) * 0.5),
            # 使风速更有波动且非负
            'rain (mm)': np.random.rand(5000) * 0.1,
            'Tdew (degC)': np.sin(np.linspace(0, 100, 5000) / 10) * 8 + 10 + np.random.randn(5000) * 0.5
        }
        dummy_df = pd.DataFrame(dummy_data)
        dummy_df.to_csv("weather.csv", index=False)
        print("虚拟 weather.csv 文件创建成功 (5000条记录)。")

    # 创建股票数据
    if not os.path.exists("stock_data.csv"):
        print("创建虚拟 stock_data.csv 文件...")
        # 创建股票数据，每天一个数据点
        dates = pd.date_range(start='2020-01-01', periods=1000, freq='D')
        # 生成一个随机游走的价格序列
        close_price = 100 + np.cumsum(np.random.normal(0, 1, 1000))
        # 确保价格为正
        close_price = np.maximum(close_price, 1)

        # 生成其他价格数据
        daily_volatility = np.random.uniform(0.005, 0.02, 1000)
        high_price = close_price * (1 + daily_volatility)
        low_price = close_price * (1 - daily_volatility)
        open_price = low_price + np.random.rand(1000) * (high_price - low_price)

        # 生成成交量数据
        volume = np.random.lognormal(10, 1, 1000) * 1000

        # 创建DataFrame
        stock_data = {
            'Date': dates,
            'Open': open_price,
            'High': high_price,
            'Low': low_price,
            'Close': close_price,
            'Volume': volume,
            'Adj Close': close_price * (1 + np.random.uniform(-0.01, 0.01, 1000))
        }
        stock_df = pd.DataFrame(stock_data)
        stock_df.to_csv("stock_data.csv", index=False)
        print("虚拟 stock_data.csv 文件创建成功 (1000条记录)。")

    # 创建能源数据
    if not os.path.exists("energy_data.csv"):
        print("创建虚拟 energy_data.csv 文件...")
        # 创建能源数据，每小时一个数据点
        dates = pd.date_range(start='2020-01-01', periods=8760, freq='H')  # 一年的小时数

        # 基础功耗模式：日内变化 + 周内变化 + 季节变化
        hour_of_day = dates.hour.values
        day_of_week = dates.dayofweek.values
        day_of_year = dates.dayofyear.values

        # 日内变化：早晚高峰
        hour_pattern = 0.5 * np.sin((hour_of_day - 6) * np.pi / 12) + 0.5

        # 周内变化：工作日比周末高
        weekday_pattern = np.where(day_of_week < 5, 1.0, 0.8)

        # 季节变化：冬夏高，春秋低
        season_pattern = 0.3 * np.sin((day_of_year - 15) * 2 * np.pi / 365) + 0.7

        # 温度模式：季节变化 + 日内变化
        temp_season = 15 * np.sin((day_of_year - 15) * 2 * np.pi / 365) + 15  # -0~30°C
        temp_daily = 5 * np.sin((hour_of_day - 6) * 2 * np.pi / 24)  # ±5°C日内波动
        temperature = temp_season + temp_daily + np.random.normal(0, 2, 8760)  # 添加随机噪声

        # 湿度：与温度负相关 + 季节变化
        humidity_base = 70 - 0.5 * temperature + 10 * np.sin((day_of_year - 90) * 2 * np.pi / 365)
        humidity = np.clip(humidity_base + np.random.normal(0, 5, 8760), 10, 100)  # 裁剪到合理范围

        # 功耗：基础模式 + 温度影响 + 随机波动
        power_base = 1000 * hour_pattern * weekday_pattern * season_pattern
        # 温度影响：温度过高或过低时功耗增加（空调、暖气）
        temp_effect = 0.2 * np.abs(temperature - 20) ** 1.5
        power_consumption = power_base + temp_effect + np.random.normal(0, 50, 8760)
        power_consumption = np.maximum(power_consumption, 100)  # 确保功耗为正

        # 创建DataFrame
        energy_data = {
            'timestamp': dates,  # 使用'timestamp'作为列名
            'power': power_consumption,
            'temperature': temperature,
            'humidity': humidity
        }
        energy_df = pd.DataFrame(energy_data)
        energy_df.to_csv("energy_data.csv", index=False)
        print("虚拟 energy_data.csv 文件创建成功 (8760条记录)。")

    # 启动 Qt 应用程序
    app = QApplication(sys.argv)
    window = WeatherAnalyzerApp()
    window.show()

    # 如果指定了自动训练参数，则在应用程序启动后自动开始训练
    if args.auto_train:
        # 使用QTimer延迟执行，确保GUI已完全加载
        from PyQt6.QtCore import QTimer
        QTimer.singleShot(2000, window.start_training)  # 2秒后自动开始训练
        print("将在2秒后自动开始训练...")

    sys.exit(app.exec())